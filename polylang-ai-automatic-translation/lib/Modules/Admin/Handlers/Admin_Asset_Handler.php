<?php
/**
 * Admin_Asset_Handler class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Admin
 */

namespace PLLAT\Admin\Handlers;

use DI\Attribute\Inject;
use XWP\DI\Decorators\Action;
use XWP\DI\Decorators\Handler;

/**
 * Handles admin assets
 */
#[Handler( tag: 'admin_init', priority: 10 )]
class Admin_Asset_Handler {
    /**
     * Admin pages
     *
     * @var array<string,string|array{0: string, 1: string}> $pages
     */
    private array $pages = array(
        'languages_page_polylang-ai-automatic-translation' => '',
        'languages_page_polylang-ai-translate-bulk'        => '',
        'nav-menus.php'                                    => '',
        'post-new.php'                                     => '',
        'post.php'                                         => array( 'action', 'edit' ),
        'term.php'                                         => array( 'taxonomy', '*' ),
    );

    /**
     * Constructor
     *
     * @param string $app_url App URL.
     * @param string $app_ver App version.
     */
    public function __construct(
        #[Inject( 'app.url' )] private string $app_url,
        #[Inject( 'app.ver' )] private string $app_ver,
    ) {
    }

    /**
     * Enqueue admin scripts and styles
     *
     * @param string $screen Screen ID.
     */
    #[Action( tag: 'admin_enqueue_scripts' )]
    public function enqueue_admin_scripts( string $screen ): void {
        if ( ! $this->is_valid_screen( $screen ) ) {
            return;
        }

        \wp_enqueue_style(
            'pllat_admin_style',
            $this->app_url . '/assets/css/admin.css',
            array(),
            $this->app_ver,
        );
        \wp_enqueue_script(
            'pllat_admin_script',
            $this->app_url . '/assets/js/admin.js',
            array( 'jquery' ),
            $this->app_ver,
            true,
        );

        \wp_localize_script(
            'pllat_admin_script',
            'PLLAT_Admin',
            array(
                'ajax_url' => \admin_url( 'admin-ajax.php' ),
                'nonce'    => \wp_create_nonce( 'pllat_admin_nonce' ),
            ),
        );
    }

    /**
     * Check if the current screen is valid
     *
     * @param  string $screen Screen ID.
     * @return bool
     */
    private function is_valid_screen( string $screen ): bool {
        if ( ! isset( $this->pages[ $screen ] ) ) {
            return false;
        }

        if ( '' === $this->pages[ $screen ] ) {
            return true;
        }

        [ $arg, $val ] = $this->pages[ $screen ];
        $req           = \xwp_fetch_req_var( $arg, '' );

        return '' !== $req && ( '*' === $val || $req === $val );
    }
}
