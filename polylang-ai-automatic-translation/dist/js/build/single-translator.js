(()=>{"use strict";const t={init:function(){this.translationTimers={},this.timerInterval=null,this.lastTimerUpdate=0,this.startTimerInterval()},startTimerInterval:function(){this.timerInterval||(this.timerInterval=setInterval((()=>{this.updateAllTimers()}),1e3))},clearTimers:function(){this.timerInterval&&(clearInterval(this.timerInterval),this.timerInterval=null),this.translationTimers={}},updateAllTimers:function(){const t=Math.floor(Date.now()/1e3);let e=!1;return Object.keys(this.translationTimers).forEach((s=>{const n=jQuery(`#pllat-translation-timer-${s}`);if(n.length){const r=this.translationTimers[s];if(r&&r>0){const s=Math.max(0,t-r),a=this.formatTime(s);n.text(a),e=!0}}})),!e&&t-this.lastTimerUpdate>30?(clearInterval(this.timerInterval),this.timerInterval=null):e&&(this.lastTimerUpdate=t),e},updateTranslationTimers:function(t,e={}){let s=!1;t.forEach((t=>{e&&e[t]?(this.translationTimers[t]=parseInt(e[t],10),s=!0):this.translationTimers[t]||(this.translationTimers[t]=Math.floor(Date.now()/1e3),s=!0)})),Object.keys(this.translationTimers).forEach((e=>{t.includes(e)||delete this.translationTimers[e]})),s&&(this.startTimerInterval(),this.updateAllTimers())},formatTime:function(t){const e=t%60;return`${Math.floor(t/60).toString().padStart(2,"0")}:${e.toString().padStart(2,"0")}`},getCurrentTimer:function(t){if(!this.translationTimers[t])return"00:00";const e=Math.floor(Date.now()/1e3),s=this.translationTimers[t];if(s&&s>0){const t=Math.max(0,e-s);return this.formatTime(t)}return"00:00"}},e={init:function(){},renderLanguageBadge:function(e,s){const n=this.getLanguageDetails(e),r=this.getStatusClasses(s)[s]||"bg-gray-100 text-gray-800 border-gray-300";let a="";return"processing"===s&&(a=`\n        <span id="pllat-translation-timer-${e}" class="inline-flex items-center text-xs font-mono bg-green-200 px-1.5 py-0.5 rounded mr-1">\n          ${t.getCurrentTimer(e)}\n        </span>`),`<div class="inline-flex items-center px-3 py-1 m-1 text-sm font-medium border rounded-md ${r}">\n      ${n.flag?`<img src="${n.flag}" alt="${n.code}" class="w-4 h-3 mr-1">`:""}\n      ${a}\n      <span class="mr-1">${n.name}</span>\n      <span class="text-xs opacity-60">${n.code}</span>\n    </div>`},getLanguageDetails:function(t){return pllat.languages&&pllat.languages[t]?pllat.languages[t]:{name:t.toUpperCase(),flag:"",code:t}},getStatusClasses:function(){return{scheduling:"bg-blue-100 text-blue-800 border-blue-300",queued:"bg-yellow-100 text-yellow-800 border-yellow-300",processing:"bg-green-100 text-green-800 border-green-300"}},renderSchedulingLanguages:function(t){return t.length>0?`\n      <div class="mb-3">\n        <h4 class="mb-1 font-medium text-gray-700">${pllat.strings.status.scheduling}:</h4>\n        <div class="flex flex-wrap">\n          ${t.map((t=>this.renderLanguageBadge(t,"scheduling"))).join("")}\n        </div>\n      </div>`:""},renderQueuedLanguages:function(t){return t.length>0?`\n      <div class="mb-3">\n        <h4 class="mb-1 font-medium text-gray-700">${pllat.strings.status.inQueue}:</h4>\n        <div class="flex flex-wrap">\n          ${t.map((t=>this.renderLanguageBadge(t,"queued"))).join("")}\n        </div>\n      </div>`:""},renderProcessingLanguages:function(t){return t.length>0?`\n      <div class="mb-3">\n        <h4 class="mb-1 font-medium text-gray-700">${pllat.strings.status.currentlyTranslating}:</h4>\n        <div class="flex flex-wrap">\n          ${t.map((t=>this.renderLanguageBadge(t,"processing"))).join("")}\n        </div>\n      </div>`:""},renderStatusContainer:function(t,e,s,n){const r=this.renderSchedulingLanguages(t),a=this.renderQueuedLanguages(e),i=this.renderProcessingLanguages(s);return r||a||i?`\n      <div>\n        <h3 class="mb-3 text-lg font-medium text-gray-800">Translation Status</h3>\n        \n      <div class="flex items-center gap-2 mb-4 status-message-container" style="display: none;">\n        <div class="loader-container">\n          ${n?'<span class="loader-ring"></span>':'<span class="loader-dots"></span>'}\n        </div>\n        <div class="status-text"></div>\n      </div>\n    \n        ${i}\n        ${r}\n        ${a}\n      </div>`:""},renderErrorMessage:function(t){return t?`\n      <div class="pllat-error-message bg-red-50 border-solid border !border-l-4 !border-red-400 text-red-800 p-6 rounded-md mb-4 shadow-sm">\n        <div class="flex items-start">\n          <div class="flex-shrink-0">\n            <span class="dashicons dashicons-warning text-red-500 text-xl"></span>\n          </div>\n          <div class="ml-3 flex-1">\n            <span class="text-md font-medium text-red-800 mb-3 text-lg block">An error occurred</span>\n            <div class="text-sm text-red-700 mb-4 font-mono whitespace-pre-wrap">${this.escapeHtml(t).replace(/\n/g,"<br>")}</div>\n            <p class="text-xs text-red-600">\n              For more detailed information, please check the \n              <a href="${window.pllat&&window.pllat.log_file_url}" class="text-red-700 hover:text-red-900 underline" target="_blank">plugin debug log file</a>.\n            </p>\n          </div>\n        </div>\n      </div>\n    `:""},renderStatusMessage:function(t,e){return`\n      <div class="translation-status-content mt-2">\n        <div>\n          <h3 class="mb-3 text-lg font-medium text-gray-800">Translation Status</h3>\n          <div class="flex items-center gap-2 mb-4 status-message-container">\n            <div class="loader-container">\n              ${e?'<span class="loader-ring"></span>':'<span class="loader-dots"></span>'}\n            </div>\n            <div class="status-text">${t}</div>\n          </div>\n        </div>\n      </div>\n    `},renderLoaderState:function(t){return t?'<span class="loader-ring"></span>':'<span class="loader-dots"></span>'},escapeHtml:function(t){return t?String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"):""}},s={init:function(){this.lastUIState={status:null,schedulingLanguages:[],queuedLanguages:[],processingLanguages:[]},this.hasScrolledToStatus=!1,this.processingActive=!1},hasUIStateChanged:function(t,e,s,n){if(this.lastUIState.status!==t)return!0;if(this.lastUIState.schedulingLanguages.length!==e.length||this.lastUIState.queuedLanguages.length!==s.length||this.lastUIState.processingLanguages.length!==n.length)return!0;const r=(t,e)=>t.some(((t,s)=>t!==e[s]));return!!(r(e,this.lastUIState.schedulingLanguages)||r(s,this.lastUIState.queuedLanguages)||r(n,this.lastUIState.processingLanguages))},updateUIState:function(t,e,s,n){this.lastUIState={status:t,schedulingLanguages:[...e],queuedLanguages:[...s],processingLanguages:[...n]}},updateProcessingState:function(t,e,s){t.length>0?this.processingActive=!0:(e.length>0||s.length>0)&&(this.processingActive=!1)},isProcessingActive:function(){return this.processingActive},markScrolledToStatus:function(){this.hasScrolledToStatus=!0},resetScrolledStatus:function(){this.hasScrolledToStatus=!1},hasUserScrolledToStatus:function(){return this.hasScrolledToStatus}},n={init:function(){e.init(),s.init(),t.init(),this.strings=window.pllat.strings,this.startButton=jQuery("#pllat_update_translations_button"),this.cancelButton=jQuery("#pllat_cancel_translations_button"),this.statusContainer=jQuery("#pllat_translation_status_container"),this.informMessage=jQuery("#pllat_update_inform_message"),this.informMessageLoader=jQuery("#pllat_update_inform_message_loader"),this.includedLanguagesSelector=jQuery("#pllat_included_languages"),this.selectAllCheckbox=jQuery("#pllat_language_select_all"),this.originalButtonText=this.startButton.text(),this.createPersistentErrorContainer()},createPersistentErrorContainer:function(){0===jQuery("#pllat_persistent_error_container").length&&jQuery('<div id="pllat_persistent_error_container" class="mt-5 mb-5 p-0.5"></div>').insertAfter(this.statusContainer),this.persistentErrorContainer=jQuery("#pllat_persistent_error_container")},bindEvents:function(t){this.startButton.on("click",(e=>t.handleTranslateButtonClick(e))),this.cancelButton.on("click",(e=>t.handleCancelButtonClick(e))),this.selectAllCheckbox.on("click",(t=>this.handleSelectAllCheckboxClick(t)))},handleSelectAllCheckboxClick:function(t){const e=jQuery(t.target),s=e.parents(".js-language-select-all-container").find(".js-text");e.is(":checked")?(this.includedLanguagesSelector.find(".js-included-language-checkbox").prop("checked",!0),s.text(pllat.strings.deselectAll)):(this.includedLanguagesSelector.find(".js-included-language-checkbox").prop("checked",!1),s.text(pllat.strings.selectAll))},getSelectedLanguages:function(){const t=[];return this.includedLanguagesSelector.find(".js-included-language-checkbox").each((function(e,s){const n=jQuery(s);if(n.is(":checked")){const e=n.val();t.push(e)}})),t},updateStatusUI:function(n,r=[],a=[],i=[],o={}){if(s.updateProcessingState(i,a,r),i.length>0&&t.updateTranslationTimers(i,o),this.updateLoaderState(),!s.hasUIStateChanged(n,r,a,i)&&this.statusContainer.find(".translation-status-content").length>0)return t.updateAllTimers(),void(this.statusContainer.is(":hidden")&&this.statusContainer.show());s.updateUIState(n,r,a,i);const l=e.renderStatusContainer(r,a,i,s.isProcessingActive());l?(this.updateStatusContent(l),t.updateAllTimers(),(i.length>0||a.length>0)&&n!==s.lastUIState.status&&this.scrollToStatus()):this.clearStatusContent()},updateStatusContent:function(t){const e=this.statusContainer.find(".translation-status-content");e.length>0?e.html(t):this.statusContainer.append(`<div class="translation-status-content mt-2">${t}</div>`),this.statusContainer.show()},clearStatusContent:function(){this.statusContainer.find(".translation-status-content").remove(),this.informMessage.is(":visible")||this.informMessageLoader.is(":visible")||this.statusContainer.hide(),t.clearTimers()},showInformMessage:function(t){if(0===this.statusContainer.find(".translation-status-content").length){const n=e.renderStatusMessage(t,s.isProcessingActive());this.statusContainer.append(n)}else{const n=this.statusContainer.find(".status-message-container"),r=n.find(".loader-container"),a=n.find(".status-text");r.html(e.renderLoaderState(s.isProcessingActive())),a.html(t),n.show()}this.statusContainer.show(),this.informMessage.html(t),this.informMessageLoader.html(e.renderLoaderState(s.isProcessingActive()))},hideInformMessage:function(){this.statusContainer.find(".status-message-container").hide(),0!==this.statusContainer.find(".translation-status-content").length&&""!==this.statusContainer.find(".translation-status-content").html().trim()||this.statusContainer.hide(),this.informMessage.text(""),this.informMessageLoader.html("")},updateLoaderState:function(){this.informMessageLoader.is(":visible")&&this.informMessageLoader.html(e.renderLoaderState(s.isProcessingActive()))},loading:function(t){t?(this.startButton.hide(),this.cancelButton.show()):this.startButton.text(this.strings.loading).attr("disabled",!0)},loaded:function(e){e||(this.startButton.attr("disabled",!1).text(this.originalButtonText).show(),this.cancelButton.hide(),s.resetScrolledStatus(),t.clearTimers())},scrollToStatus:function(){!s.hasUserScrolledToStatus()&&this.statusContainer.length&&this.statusContainer.is(":visible")&&(s.markScrolledToStatus(),setTimeout((()=>{this.statusContainer[0].scrollIntoView({behavior:"smooth",block:"start"})}),100))},displayPersistentError:function(t){if(!t)return;const s=e.renderErrorMessage(t);this.persistentErrorContainer.find(".pllat-error-message").length>0?this.persistentErrorContainer.find(".pllat-error-message").replaceWith(s):this.persistentErrorContainer.html(s);const n=jQuery('<div class="flex justify-end mt-2"><button type="button" class="pllat-dismiss-error border-red-600 flex items-center justify-center border-solid border rounded-md px-2 py-1 bg-none cursor-pointer"><span class="dashicons dashicons-dismiss text-sm mr-1 text-red-500" style="margin-top: 3px;"></span>Dismiss Error</button></div>');this.persistentErrorContainer.find(".pllat-error-message").append(n),n.find("button").on("click",(()=>{this.clearPersistentError(),this.clearTranslationError()})),this.persistentErrorContainer.show()},hasPersistentError:function(){return this.persistentErrorContainer&&this.persistentErrorContainer.is(":visible")&&this.persistentErrorContainer.find(".pllat-error-message").length>0},clearPersistentError:function(){this.persistentErrorContainer.empty().hide()},clearTranslationError:function(){const t=this.startButton.data("id"),e=this.startButton.data("type");if(!t)return;const s={action:"term"===e?"pllat_clear_term_translation_error":"pllat_clear_post_translation_error",id:t,nonce:pllat.nonce};jQuery.post(ajaxurl,s)}},r={handleTranslationError:function(t,e,s){let n=this.extractErrorMessage(t);return s.errorOccurred.replace("%s",e).replace("%s",n)},handleStatusError:function(t){return t.errors&&t.errors.length>0?Array.isArray(t.errors)?t.errors.join("\n"):t.errors:pllat.strings.genericError||"An error occurred during translation"},handleStatusCheckError:function(t){return"Error checking translation status: "+this.extractErrorMessage(t)},extractErrorMessage:function(t){return"object"==typeof t&&null!==t?t.message||t.responseText||t.responseJSON&&t.responseJSON.message||JSON.stringify(t):String(t)},logError:function(t,e){const s=this.extractErrorMessage(t);console.error(`[${e}] ${s}`,t)}},a={init:function(){this.statusCheckInterval=null,this.isProcessing=!1,n.init(),this.postId=n.startButton.data("id"),this.entityType=n.startButton.data("type");const t={handleTranslateButtonClick:t=>this.handleTranslateButtonClick(t),handleCancelButtonClick:t=>this.handleCancelButtonClick(t)};n.bindEvents(t),this.checkForPendingTranslations(),this.checkForErrors()},checkForPendingTranslations:function(){this.postId&&this.performStatusCheck()},checkForErrors:function(){this.postId&&this.performErrorCheck()},performErrorCheck:async function(){try{const s=await(t=this.entityType,e=this.postId,new Promise(((s,n)=>{if(!e)return void s({has_error:!1});const r="term"===t?"pllat_get_term_translation_errors":"pllat_get_post_translation_errors";jQuery.post(ajaxurl,{action:r,id:e},(t=>{t.success?s(t.data):n(t.data?.message||"Error checking for translation errors")})).fail((t=>{n(t)}))})));s.has_error&&s.error_message&&n.displayPersistentError(s.error_message)}catch(t){r.logError(t,"Error Check")}var t,e},clearTranslationErrors:async function(){try{await(t=this.entityType,e=this.postId,new Promise(((s,n)=>{if(!e)return void s({success:!0});const r="term"===t?"pllat_clear_term_translation_error":"pllat_clear_post_translation_error";jQuery.post(ajaxurl,{action:r,id:e},(t=>{t.success?s(t.data||{success:!0}):n(t.data?.message||"Error clearing translation errors")})).fail((t=>{n(t)}))})))}catch(t){r.logError(t,"Clear Error")}var t,e},handleTranslateButtonClick:async function(t){t.preventDefault();const e=this.gatherFormData();if(0===e.languages.length)return n.loaded(this.isProcessing),n.hideInformMessage(),void alert(n.strings.noLanguagesSelected);await this.startTranslationProcess(e)},gatherFormData:function(){return{type:n.startButton.data("type"),entity:n.startButton.data("entity"),id:n.startButton.data("id"),instructions:jQuery("#pllat_additional_instructions_field").val(),force:jQuery("#pllat_force_translation").is(":checked"),languages:n.getSelectedLanguages()}},confirmTranslationAction:function(){return!1!==confirm(n.strings.confirm)},startTranslationProcess:async function(t){n.scrollToStatus(),n.loading(this.isProcessing),n.showInformMessage(n.strings.loading,t.instructions?"Instructions: "+t.instructions:null),await this.clearTranslationErrors(),n.clearPersistentError();try{const e=await this.queueTranslations(t);if(e.isEmpty)return;await this.processQueuedLanguages(e,t),this.startStatusMonitoring(t.languages)}catch(t){this.handleProcessError(t)}},queueTranslations:async function(t){const e=await function(t,e,s,n,r=!1){return new Promise(((a,i)=>{let o={action:"pllat_queue_"+t+"_translations",languages:n,id:e,entity:s,force:r};jQuery.post(ajaxurl,o,(function(t){t.success?a(t.data.translation_queue):i(t.data?.message)})).fail((function(t){i(t)}))}))}(t.type,t.id,t.entity,t.languages,t.force);if(0===Object.keys(e).length)return n.loaded(this.isProcessing),n.hideInformMessage(),alert(n.strings.noTranslations),{isEmpty:!0};const s=[...Object.values(e)];return n.updateStatusUI("scheduling",s,[],[]),this.triggerActionSchedulerProcessing(),e},triggerActionSchedulerProcessing:function(){wp.apiRequest({path:"/pllat/v1/action-scheduler/trigger",method:"POST",error:t=>{r.logError(t,"Action Scheduler Trigger")}})},processQueuedLanguages:async function(t,e){if(t.isEmpty)return;const s=[...Object.values(t)];for(const a of Object.values(t)){const t=a.toUpperCase();try{await this.processLanguage(a,t,s,e)}catch(e){const s=r.handleTranslationError(e,t,n.strings);throw r.logError(e,"Language Processing"),n.hideInformMessage(),alert(s),e}}},processLanguage:async function(t,e,s,r){n.showInformMessage(n.strings.currentlyScheduling.replace("%s",e),r.instructions?`Instructions for ${e}: ${r.instructions}`:null);const a=[t],i=s.filter((e=>e!==t));n.updateStatusUI("scheduling",a,i,[]),await function(t,e,s,n,r,a=!1){return new Promise(((i,o)=>{jQuery.post(ajaxurl,{action:"pllat_process_"+t+"_translation",id:e,entity:s,language_slug:n,instructions:r,force:a},(function(t){t.success?i(t.data.scheduled):o(t.data?.message)})).fail((function(t){o(t)}))}))}(r.type,r.id,r.entity,t,r.instructions,r.force)},startStatusMonitoring:function(t){this.isProcessing=!0,n.showInformMessage("Waiting in queue: "+t.map((t=>t.toUpperCase())).join(", "),"Languages will be translated one by one"),n.updateStatusUI("queued",[],t,[]),this.startStatusCheck()},handleProcessError:function(t){n.loaded(this.isProcessing),n.hideInformMessage();const e=t.message||String(t);n.displayPersistentError(e),r.logError(t,"Translation Process")},handleCancelButtonClick:async function(t){t.preventDefault();try{const t=n.cancelButton.data("type"),e=n.cancelButton.data("id"),s=await function(t,e){return new Promise(((s,n)=>{const r="term"===t?"pllat_cancel_term_translations":"pllat_cancel_post_translations";jQuery.post(ajaxurl,{action:r,id:e},(function(t){t.success?s(t.data):n(t.data?.message||"Unknown error")})).fail((function(t){n(t)}))}))}(t,e);this.isProcessing=!1,n.loaded(this.isProcessing),n.hideInformMessage(),this.stopStatusCheck(),n.updateStatusUI("completed"),alert(s.message)}catch(t){r.logError(t,"Cancel Translation"),alert(n.strings.errorCancelling)}},startStatusCheck:function(){this.statusCheckInterval&&clearInterval(this.statusCheckInterval),this.performStatusCheck(),this.statusCheckInterval=setInterval((()=>this.performStatusCheck()),3e3)},stopStatusCheck:function(){this.statusCheckInterval&&(clearInterval(this.statusCheckInterval),this.statusCheckInterval=null)},performStatusCheck:async function(){try{const s=await(t=this.postId,e=this.entityType,new Promise(((s,n)=>{if(!t)return void s({status:"completed"});const r="term"===e?"pllat_check_term_translation_status":"pllat_check_post_translation_status";jQuery.post(ajaxurl,{action:r,id:t},(t=>{t.success?s(t.data):n(t.data?.message||"An error occurred while checking translation status")})).fail((t=>{n(t)}))})));this.handleStatusResponse(s)}catch(t){this.handleStatusCheckError(t)}var t,e},handleStatusResponse:function(t){if("error"!==t.status){if("processing"!==t.status)if("completed"!==t.status);else{if(t.has_error&&t.errors&&t.errors.length>0){const e=Array.isArray(t.errors)?t.errors.join("\n"):t.errors;n.displayPersistentError(e)}this.handleCompletedStatus()}else if(this.handleProcessingStatus(t),t.has_error&&t.errors&&t.errors.length>0){const e=Array.isArray(t.errors)?t.errors.join("\n"):t.errors;n.displayPersistentError(e)}}else this.handleErrorStatus(t)},handleErrorStatus:function(t){this.isProcessing=!1,n.loaded(this.isProcessing),this.stopStatusCheck();const e=r.handleStatusError(t);n.displayPersistentError(e),n.hideInformMessage()},handleProcessingStatus:function(t){this.isProcessing=!0,n.loading(this.isProcessing),this.startStatusCheck();const e=t.pending_languages||[],s=t.processing_languages||[],r=t.processing_timestamps||{};n.updateStatusUI("processing",[],e,s,r),s.length>0?(n.showInformMessage(pllat.strings.status.inProgress),n.scrollToStatus()):e.length>0?n.showInformMessage(pllat.strings.status.waiting):n.showInformMessage(t.message)},handleCompletedStatus:function(){this.isProcessing&&(this.isProcessing=!1,n.loaded(this.isProcessing),n.hideInformMessage(),this.stopStatusCheck(),n.updateStatusUI("completed"),n.hasPersistentError()||(alert(n.strings.success),window.location.reload()))},handleStatusCheckError:function(t){r.logError(t,"Status Check");const e=r.handleStatusCheckError(t);n.displayPersistentError(e)}};jQuery(document).ready((function(){a.init()}))})();