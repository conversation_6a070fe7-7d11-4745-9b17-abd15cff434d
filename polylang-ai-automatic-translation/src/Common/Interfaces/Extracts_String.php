<?php
/**
 * Extracts_String interface file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Common
 */

namespace PLLAT\Common\Interfaces;

/**
 * Defines a common interface for extracting translatable strings from a given content.
 */
interface Extracts_String {
    /**
     * Extract translatable strings from a given content.
     *
     * @param  string $content The content to extract strings from.
     * @return array<mixed,mixed>
     */
    public function extract_strings( string $content ): array;
}
