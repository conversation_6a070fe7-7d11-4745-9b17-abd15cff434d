<?php
/**
 * Loads the action scheduler library.
 *
 * @package    Polylang AI Automatic Translation
 * @subpackage Common
 */

if ( ! function_exists( 'pllat_load_as' ) && function_exists( 'add_action' ) ) :
    /**
     * Load the Action Scheduler library.
     */
    function pllat_load_as(): void {
        if ( function_exists( 'WC' ) || function_exists( 'action_scheduler_register_3_dot_9_dot_2' ) ) {
            return;
        }

        require_once PLLAT_PLUGIN_DIR . '/vendor/woocommerce/action-scheduler/action-scheduler.php';
    }

    add_action( 'plugins_loaded', 'pllat_load_as', -10, 0 );
endif;

if ( function_exists( 'add_action' ) ) :
    add_action(
        'init',
        static function () {
            if ( ! is_admin() || did_action( 'pllat_loaded' ) || pllat_is_pll_deactivating() ) {
                return;
            }

            add_action(
                'admin_notices',
                static function () {
                    wp_admin_notice(
                        esc_html__(
                            'Polylang AI Automatic Translation requires Polylang or Polylang Pro to be active.',
                            'polylang-ai-autotranslate',
                        ),
                        array(
                            'id'             => 'pllat-no-polylang',
                            'paragraph_wrap' => true,
                            'type'           => 'error',
                        ),
                    );
                },
            );
        },
        -1,
    );
endif;

if ( function_exists( 'register_activation_hook' ) ) :
    register_activation_hook(
        PLLAT_PLUGIN_FILE,
        static function () {
            add_action(
                'activated_plugin',
                static function ( $plugin ) {
                    if ( PLLAT_PLUGIN_BASE !== $plugin || ! defined( 'POLYLANG_FILE' ) ) {
                        return;
                    }

                    wp_safe_redirect(
                        add_query_arg(
                            array(
                                'page' => 'polylang-ai-automatic-translation',
                                'tab'  => 'license',
                            ),
                            admin_url( 'admin.php' ),
                        ),
                    );
                    exit;
                },
            );
        },
    );
endif;
