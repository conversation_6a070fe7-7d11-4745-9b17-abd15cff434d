<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\Elementor;

/**
 * Validates the keys of elementor widgets and settings in the elementor
 * data and determines if the content should be translated.
 */
class Key_Validator {
    /**
     * Holds the keys that should be translated anytime they are found.
     *
     * @var array<string>
     */
    private array $translatable_keys;
    /**
     * Holds the keys that should not be translated.
     *
     * @var array<string>
     */
    private array $non_translatable_keys;
    /**
     * Hold keys that if found in the key, the content should not be translated.
     *
     * @var array<string>
     */
    private array $non_translatable_patterns;
    /**
     * Hold keys that if found in the key, the content should be translated.
     *
     * @var array<string>
     */
    private array $translatable_patterns;

    /**
     * Constructor.
     *
     * @param array<string> $custom_translatable_keys The keys that should be translated anytime they are found.
     */
    public function __construct( array $custom_translatable_keys = array() ) {
        $this->translatable_keys = $custom_translatable_keys;
        $this->init_validation_rules();
    }

    /**
     * Initializes the validation rules to determine if the content should be translated.
     */
    private function init_validation_rules(): void {
        $this->translatable_keys = array(
            'editor',
        );

        // Keys that should never be translated
        $this->non_translatable_keys = array(
            'type',
            'elType',
            'isInner',
            'widgetType',
            'id',
            'className',
            'anchor',
            'rel',
            'linkTarget',
            'style',
            'blockName',
            'tagName',
            'namespace',
            'align',
            'mode',
            'level',
            'lock',
            'ref',
            'role',
            'method',
            'action',
            'target',
            'class',
            'isValid',
            'templateLock',
            'version',
            'library',
            'size',
            'sizes',
            'unit',
        );

        // Patterns indicating non-translatable content
        $this->non_translatable_patterns = array(
            'align',
            'position',
            'order',
            'width',
            'height',
            'size',
            'spacing',
            'padding',
            'margin',
            'border',
            'color',
            'background',
            'layout',
            'orientation',
            'direction',
            'style',
            'flex',
            'grid',
            'gap',
            'offset',
            'columns',
            'url',
            'link',
            'href',
            'animation',
            'motion',
            'transition',
            'transform',
            'gradient',
            'opacity',
            'shadow',
            'radius',
            'weight',
            'custom',
            'default',
            'active',
            'hover',
            'focus',
            'disabled',
            'selected',
            'typography',
            'font',
            'html_tag',
            'separator',
        );

        // Patterns indicating translatable content
        $this->translatable_patterns = array(
            'text',
            'title',
            'heading',
            'content',
            'description',
            'author',
            'name',
            'subtitle',
            'caption',
            'excerpt',
            'quote',
            'label',
            'alt',
            'value',
            'placeholder',
            'message',
            'question',
            'answer',
            'button',
            'link',
            'testimonial',
            'info',
            'instructions',
            'review',
        );
    }

    /**
     * Determines if the content should be translated.
     *
     * @param string $key The key to check.
     * @return bool True if the content should be translated, false otherwise.
     */
    public function should_translate( string $key ): bool {
        // Skip meta fields
        if ( $this->is_meta_field( $key ) ) {
            return false;
        }

        // Check custom translatable keys
        if ( $this->is_custom_translatable( $key ) ) {
            return true;
        }

        // Check non-translatable keys
        if ( $this->is_non_translatable( $key ) ) {
            return false;
        }

        // Check non-translatable patterns
        if ( $this->matches_non_translatable_pattern( $key ) ) {
            return false;
        }

        // Check translatable patterns
        if ( $this->matches_translatable_pattern( $key ) ) {
            return true;
        }

        return false;
    }

    /**
     * Determines if the key is a meta field.
     *
     * @param string $key The key to check.
     * @return bool True if the key is a meta field, false otherwise.
     */
    private function is_meta_field( string $key ): bool {
        return \str_starts_with( $key, '_' );
    }

    /**
     * Determines if the key is a custom translatable field.
     *
     * @param string $key The key to check.
     * @return bool True if the key is a custom translatable field, false otherwise.
     */
    private function is_custom_translatable( string $key ): bool {
        return \in_array( $key, $this->translatable_keys );
    }

    /**
     * Determines if the key is a non-translatable field.
     *
     * @param string $key The key to check.
     * @return bool True if the key is a non-translatable field, false otherwise.
     */
    private function is_non_translatable( string $key ): bool {
        return \in_array( $key, $this->non_translatable_keys );
    }

    /**
     * Determines if the key matches a non-translatable pattern.
     *
     * @param string $key The key to check.
     * @return bool True if the key matches a non-translatable pattern, false otherwise.
     */
    private function matches_non_translatable_pattern( string $key ): bool {
        $key = \strtolower( $key );
        foreach ( $this->non_translatable_patterns as $pattern ) {
            if ( false !== \stripos( $key, $pattern ) ) {
                return true;
            }
        }
        return false;
    }

    private function matches_translatable_pattern( string $key ): bool {
        $key = \strtolower( $key );
        foreach ( $this->translatable_patterns as $pattern ) {
            if ( false !== \stripos( $key, $pattern ) ) {
                return true;
            }
        }
        return false;
    }
}
