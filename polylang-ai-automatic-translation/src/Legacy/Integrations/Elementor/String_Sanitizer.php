<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\Elementor;

class String_Sanitizer {
    public function sanitize_string( $string ): string {
        if ( ! \is_string( $string ) ) {
            return '';
        }

        $string = \html_entity_decode( $string, ENT_QUOTES | ENT_HTML5, 'UTF-8' );
        $string = \Normalizer::normalize( $string, \Normalizer::FORM_C );

        return $string;
    }
}
