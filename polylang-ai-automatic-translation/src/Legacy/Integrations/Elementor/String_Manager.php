<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\Elementor;

class String_Manager {
    private $key_validator;
    private $string_sanitizer;

    public function __construct( Key_Validator $key_validator, String_Sanitizer $string_sanitizer ) {
        $this->key_validator    = $key_validator;
        $this->string_sanitizer = $string_sanitizer;
    }

    public static function generate_hash( string $element_id, string $key ): string {
        return \md5( $element_id . '|' . $key );
    }

    public function extract_strings( array $elementor_data, array $change_hashes = array() ): array {
        $strings = array();

        foreach ( $elementor_data as $element ) {
            if ( ! isset( $element['id'] ) ) {
                continue;
            }

            $element_id = $element['id'];

            if ( isset( $element['settings'] ) ) {
                $this->extract_strings_from_settings(
                    $element['settings'],
                    $element_id,
                    $strings,
                    $change_hashes,
                );
            }

            if ( ! isset( $element['elements'] ) || ! \is_array( $element['elements'] ) ) {
                continue;
            }

            $nested_strings = $this->extract_strings( $element['elements'], $change_hashes );
            $strings        = \array_merge( $strings, $nested_strings );
        }

        return $strings;
    }

    private function extract_strings_from_settings( array $settings, string $element_id, array &$strings, array $change_hashes = array(), string $prefix = '' ): void {
        foreach ( $settings as $key => $value ) {
            $full_key = $prefix ? "{$prefix}.{$key}" : $key;

            if ( ! $this->should_process_value( $value, $key ) ) {
                continue;
            }

            if ( \is_array( $value ) ) {
                $this->process_array_value( $value, $key, $element_id, $strings, $change_hashes, $full_key );
            } else {
                $this->process_string_value( $value, $key, $element_id, $full_key, $strings, $change_hashes );
            }
        }
    }

    private function should_process_value( $value, string $key ): bool {
        if ( empty( $value ) ) {
            return false;
        }

        if ( ! \is_string( $value ) && ! \is_array( $value ) ) {
            return false;
        }

        return ! \is_string( $value ) || $this->key_validator->should_translate( $key );
    }

    private function process_array_value( array $value, string $key, string $element_id, array &$strings, array $change_hashes, string $full_key ): void {
        if ( $this->is_repeatable_structure( $value ) ) {
            $this->extract_strings_from_repeatable( $key, $value, $element_id, $strings, $change_hashes );
        } else {
            $this->extract_strings_from_settings( $value, $element_id, $strings, $change_hashes, $full_key );
        }
    }

    private function process_string_value( string $value, string $key, string $element_id, string $full_key, array &$strings, array $change_hashes ): void {
        $hash = self::generate_hash( $element_id, $full_key );
        if ( ! empty( $change_hashes ) && ! \in_array( $hash, $change_hashes ) ) {
            return;
        }

        $strings[ $hash ] = array(
            'key'   => $full_key,
            'value' => $value,
        );
    }

    private function extract_strings_from_repeatable( string $parent_key, array $items, string $element_id, array &$strings, array $change_hashes = array() ): void {
        foreach ( $items as $index => $item ) {
            if ( ! \is_array( $item ) ) {
                continue;
            }

            foreach ( $item as $field_key => $field_value ) {
                if ( \is_array( $field_value ) ) {
                    $this->extract_strings_from_settings(
                        array( $field_key => $field_value ),
                        $element_id,
                        $strings,
                        $change_hashes,
                        "{$parent_key}.{$index}",
                    );
                } elseif (\is_string( $field_value ) && ! empty( $field_value ) && $this->key_validator->should_translate( $field_key  )) {
                    $full_key = "{$parent_key}.{$index}.{$field_key}";
                    $hash     = self::generate_hash( $element_id, $full_key );

                    if ( empty( $change_hashes ) || \in_array( $hash, $change_hashes ) ) {
                        $strings[ $hash ] = array(
                            'key'   => $full_key,
                            'value' => $field_value,
                        );
                    }
                }
            }
        }
    }

    public function replace_strings( array $elementor_data, array $strings_data, array $translated_strings ): array {
        if ( empty( $translated_strings ) ) {
            return $elementor_data;
        }

        foreach ( $elementor_data as &$element ) {
            if ( ! isset( $element['id'] ) ) {
                continue;
            }

            $element_id = $element['id'];

            if ( isset( $element['settings'] ) ) {
                $element['settings'] = $this->replace_strings_in_settings(
                    $element['settings'],
                    $element_id,
                    $strings_data,
                    $translated_strings,
                );
            }

            if ( ! isset( $element['elements'] ) || ! \is_array( $element['elements'] ) ) {
                continue;
            }

            $element['elements'] = $this->replace_strings(
                $element['elements'],
                $strings_data,
                $translated_strings,
            );
        }

        return $elementor_data;
    }

    private function replace_strings_in_settings( array $settings, string $element_id, array $original, array $translated, string $prefix = '' ): array {
        foreach ( $settings as $key => &$value ) {
            $full_key = $prefix ? "{$prefix}.{$key}" : $key;

            if ( \is_array( $value ) ) {
                $value = $this->is_repeatable_structure( $value ) ? $this->replace_strings_in_repeatable(
                    $key,
                    $value,
                    $element_id,
                    $original,
                    $translated,
                ) : $this->replace_strings_in_settings(
                    $value,
                    $element_id,
                    $original,
                    $translated,
                    $full_key,
                );
            } elseif ( \is_string( $value ) && ! empty( $value ) && $this->key_validator->should_translate( $key ) ) {
                $hash = self::generate_hash( $element_id, $full_key );
                if ( isset( $translated[ $hash ] ) ) {
                    $value = $this->sanitize_string( $translated[ $hash ] );
                }
            }
        }

        return $settings;
    }

    private function replace_strings_in_repeatable( string $parent_key, array $items, string $element_id, array $original, array $translated ): array {
        foreach ( $items as $index => &$item ) {
            if ( ! \is_array( $item ) ) {
                continue;
            }

            foreach ( $item as $field_key => &$field_value ) {
                if ( \is_array( $field_value ) ) {
                    $field_value = $this->replace_strings_in_settings(
                        array( $field_key => $field_value ),
                        $element_id,
                        $original,
                        $translated,
                        "{$parent_key}.{$index}",
                    )[ $field_key ];
                } elseif ( \is_string( $field_value ) && ! empty( $field_value ) && $this->key_validator->should_translate( $field_key ) ) {
                    $full_key = "{$parent_key}.{$index}.{$field_key}";
                    $hash     = self::generate_hash( $element_id, $full_key );

                    if ( isset( $translated[ $hash ] ) ) {
                        $field_value = $this->sanitize_string( $translated[ $hash ] );
                    }
                }
            }
        }

        return $items;
    }

    private function is_repeatable_structure( array $array ): bool {
        if ( empty( $array ) ) {
            return false;
        }

        if ( ! \is_numeric( \array_key_first( $array ) ) ) {
            return false;
        }

        return ! \in_array( false, \array_map( 'is_array', $array ), true );
    }

    private function sanitize_string( $string ) {
        return $this->string_sanitizer->sanitize_string( $string );
    }
}
