<?php
/**
 * Settings class file.
 *
 * @package    Polylang AI Automatic Translation
 * @subpackage Settings
 */

namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use Automattic\Jetpack\Constants;

/**
 * <PERSON>les setting fetching and caching for the plugin.
 */
class Settings {
    /**
     * Cached options
     *
     * @var array<string,mixed>
     */
    private static array $data = array();

    /**
     * Check whether debug mode is enabled
     *
     * @return bool
     */
    public static function is_debug_mode(): bool {
        return (bool) self::get_opt( 'pllat_debug_mode', false );
    }

    /**
     * Get the active translation API
     *
     * @return string The active translation API (e.g., 'openai', 'google', etc.)
     */
    public static function get_active_translation_api(): string {
        return self::get_opt( 'pllat_translator_api', 'openai' );
    }

    /**
     * Get the API key of the active translation API
     *
     * @return string The API key for the active translation API
     */
    public static function get_active_translation_api_key(): string {
        return self::get_translation_api_key( self::get_active_translation_api() );
    }

    /**
     * Get the translation model of a specific translation API
     *
     * @param  string $api Translation API. Defaults to the active API.
     * @return string The translation model for the specified API
     */
    public static function get_translation_model( ?string $api = null ): string {
        $api ??= self::get_active_translation_api();
        return self::get_opt( "pllat_{$api}_translation_model", 'gpt-4o' );
    }

    /**
     * Get the API key of a specific translation API
     *
     * @param  string $api The translation API (e.g., 'openai', 'google', etc.).
     * @return string The API key for the specified API
     */
    public static function get_translation_api_key( string $api ): string {
        return self::get_opt( "pllat_{$api}_api_key", '' );
    }

    /**
     * This is context set by the user to help the AI with the translation
     *
     * @return string The context for the AI translation
     */
    public static function get_website_ai_context(): string {
        return self::get_opt( 'pllat_website_ai_context', '' );
    }

    /**
     * Check if Elementor is active
     *
     * @return bool
     */
    public static function is_elementor_active(): bool {
        return Constants::is_defined( 'ELEMENTOR_VERSION' );
    }

    /**
     * Get the translatable keys for WP blocks
     *
     * @return array<string>
     */
    public static function get_wp_block_translatable_keys(): array {
        $standard = array(
            'titleText',
            'buttonText',
            'text',
            'Title',
            'title',
            'Description',
            'description',
            'placeholder',
            'feedbackText',
            'successMessage',
            'errorMessage',
            'instructions',
        );

        return self::filter_keys( $standard, 'wp_block' );
    }

    /**
     * Get the translatable keys for Elementor
     *
     * @return array<string>
     */
    public static function get_elementor_translatable_keys(): array {
        $standard = array(
            'title',
            'text',
            'editor',
            'button_text',
            'link_text',
            'title_text',
            'description_text',
            'testimonial_content',
            'testimonial_name',
            'testimonial_job',
            'service_title',
            'service_description',
            'faq_question',
            'faq_answer',
            'pricing_plan_title',
            'pricing_plan_price',
            'pricing_plan_description',
            'tab_title',
            'tab_content',
            'accordion_title',
            'accordion_content',
            'subtitle',
            'label',
            'heading',
            'sub_heading',
            'caption',
            'placeholder',
            'excerpt',
            'button_label',
            'link_label',
            'image_caption',
            'image_alt',
            'counter_title',
            'progress_bar_title',
            'form_field_label',
            'form_field_placeholder',
            'form_submit_button',
            'alert_title',
            'alert_text',
            'price_table_title',
            'footer_additional_info',
            'period',
        );

        return self::filter_keys( $standard, 'elementor' );
    }

    /**
     * Get option value and cache it
     *
     * @param  string $opt     Option name.
     * @param  mixed  $def     Default value if option is not set.
     * @param  bool   $refetch Whether to refetch the option value.
     * @return mixed
     */
    private static function get_opt( string $opt, mixed $def, bool $refetch = false ): mixed {
        if ( ! isset( self::$data[ $opt ] ) || $refetch ) {
            self::$data[ $opt ] = \get_option( $opt, $def );
        }

        return self::$data[ $opt ];
    }

    /**
     * Filter translatable keys for WP blocks or Elementor
     *
     * @param  array<string>          $standard Standard keys.
     * @param  'wp_block'|'elementor' $option   Option type.
     * @return array<string>
     */
    private static function filter_keys( array $standard, string $option ): array {
        $field  = "pllat_{$option}_translatable_keys";
        $custom = \array_diff( (array) self::get_opt( $field, array() ), $standard );

        $combined = $custom
            ? \array_merge( $standard, $custom )
            : $standard;

        /**
         * Filter the translatable keys for WP blocks or elementor
         *
         * @param array<string> $combined Translatable keys.
         * @param array<string> $standard Default keys.
         * @param array<string> $custom  Custom keys.
         *
         * @since 1.0.0
         */
        return \apply_filters( $field, $combined, $standard, $custom );
    }
}
