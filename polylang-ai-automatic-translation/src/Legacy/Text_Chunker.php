<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Token_Encoder_Interface;

class Text_Chunker {

    private $encoder;
    private const SENTENCE_END_PATTERN = '/(?<=[.!?])\s+/';
    private const SPACE_PATTERN = '/\s+/';

    public function __construct(Token_Encoder_Interface $encoder) {
        $this->encoder = $encoder;
    }

    public function chunk(string $text, int $max_tokens): array {
        $sentences = $this->split_into_sentences($text);
        return $this->process_sentences($sentences, $max_tokens);
    }

    private function split_into_sentences(string $text): array {
        return preg_split(self::SENTENCE_END_PATTERN, $text, -1, PREG_SPLIT_NO_EMPTY);
    }

    private function process_sentences(array $sentences, int $max_tokens): array {
        $chunks = [];
        $current_chunk = "";
        $current_tokens = 0;

        foreach ($sentences as $sentence) {
            $sentence_tokens = $this->count_tokens($sentence);
            
            if ($this->should_start_new_chunk($current_tokens, $sentence_tokens, $max_tokens)) {
                $this->add_chunk($chunks, $current_chunk, $current_tokens);
                $current_chunk = "";
                $current_tokens = 0;
                
                if ($sentence_tokens > $max_tokens) {
                    $chunks = array_merge($chunks, $this->split_by_spaces($sentence, $max_tokens));
                    continue;
                }
            }

            $current_chunk .= $sentence . " ";
            $current_tokens += $sentence_tokens;
        }

        $this->add_chunk($chunks, $current_chunk, $current_tokens);

        return $chunks;
    }

    private function split_by_spaces(string $text, int $max_tokens): array {
        $words = preg_split(self::SPACE_PATTERN, $text, -1, PREG_SPLIT_NO_EMPTY);
        $sub_chunks = [];
        $current_sub_chunk = "";
        $current_sub_tokens = 0;

        foreach ($words as $word) {
            $word_tokens = $this->count_tokens($word);
            
            if ($this->should_start_new_chunk($current_sub_tokens, $word_tokens, $max_tokens)) {
                $this->add_chunk($sub_chunks, $current_sub_chunk, $current_sub_tokens);
                $current_sub_chunk = "";
                $current_sub_tokens = 0;
            }
            
            $current_sub_chunk .= $word . " ";
            $current_sub_tokens += $word_tokens;
        }

        $this->add_chunk($sub_chunks, $current_sub_chunk, $current_sub_tokens);

        return $sub_chunks;
    }

    private function should_start_new_chunk(int $current_tokens, int $new_tokens, int $max_tokens): bool {
        return $current_tokens + $new_tokens > $max_tokens;
    }

    private function add_chunk(array &$chunks, string $chunk, int $tokens): void {
        if (empty($chunk)) return;
        $chunks[] = [
            'content' => trim($chunk), 
            'tokens' => $tokens
        ];
    }

    private function count_tokens(string $text): int {
        return $this->encoder->count_tokens($text);
    }

    public static function glue_chunks(array $chunks): string {
        return array_reduce($chunks, function($result, $chunk) {
            $content = $chunk['content'];
            if ($result !== '' && !preg_match('/\s$/', $result) && !preg_match('/^\s/', $content)) {
                $result .= ' ';
            }
            return $result . $content;
        }, '');
    }
}