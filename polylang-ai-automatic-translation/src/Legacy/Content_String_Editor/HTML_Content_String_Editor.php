<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Content_String_Editor;

use voku\helper\HtmlDomParser;
use voku\helper\SimpleHtmlDomInterface;

/**
 * Extracts strings from HTML content and replaces them with their translations.
 *
 * @property-read int $nodeType
 * @property-read int $childElementCount
 * @property-read string $textContent
 */
class HTML_Content_String_Editor extends Abstract_Content_String_Editor {
    const NODE_TYPE_ELEMENT = 1;  // Element nodes like <p>, <div>, etc.
    const NODE_TYPE_TEXT    = 3;  // Text nodes.
    const NODE_TYPE_COMMENT = 8;  // Comment nodes.

    const ALLOWED_TAGS = array(
        'span',
        'strong',
        'em',
        'a',
        'i',
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'p',
        'li',
        'b',
        'blockquote',
        'pre',
        'small',
        'u',
        'strike',
    );

    const EXCLUDED_TAGS = array(
        'script',
        'style',
        'iframe',
        'meta',
        'noscript',
        'object',
        'param',
        'svg',
        'video',
        'audio',
        'canvas',
        'embed',
        'map',
    );

    const ALLOWED_ATTRIBUTES = array(
        'alt',
        'title',
    );

    /**
     * @var array<int, string>
     */
    private array $extracted_strings = array();

    /**
     * @var array<string, string>
     */
    private array $string_replacements = array();

    /**
     * @var array<string, string>
     */
    private array $attribute_string_replacements = array();

    /**
     * @return array<int, string>
     */
    public function get_extracted_strings(): array {
        return \array_unique( $this->extracted_strings );
    }

    /**
     * @return array<string, string>
     */
    public function get_string_replacements(): array {
        return $this->string_replacements;
    }

    /**
     * @return array<string, string>
     */
    public function get_attribute_string_replacements(): array {
        return $this->attribute_string_replacements;
    }

    /**
     * Get the allowed tags
     *
     * @return array<int, string>
     */
    private function get_allowed_tags(): array {
        if ( ! \function_exists( 'apply_filters' ) ) {
            return self::ALLOWED_TAGS;
        }
        return \apply_filters( 'pllat_content_strip_allowed_tags', self::ALLOWED_TAGS );
    }

    /**
     * Get the excluded tags
     *
     * @return array<int, string>
     */
    private function get_excluded_tags(): array {
        if ( ! \function_exists( 'apply_filters' ) ) {
            return self::EXCLUDED_TAGS;
        }
        return \apply_filters( 'pllat_content_strip_excluded_tags', self::EXCLUDED_TAGS );
    }

    /**
     * Get the allowed attributes
     *
     * @return array<int, string>
     */
    private function get_allowed_attributes(): array {
        if ( ! \function_exists( 'apply_filters' ) ) {
            return self::ALLOWED_ATTRIBUTES;
        }
        return \apply_filters( 'pllat_content_strip_allowed_attributes', self::ALLOWED_ATTRIBUTES );
    }

    /**
     * Extract content strings from HTML for translation
     *
     * @param string $content The HTML content
     * @return array<int, string> Array of strings for translation
     */
    public function extract_strings( string $content ): array {
        $dom      = HtmlDomParser::str_get_html( $content );
        $elements = $dom->find( '*' );

        // First traverse the allowed tags elements to extract strings from the text content
        foreach ( $elements as $element ) {
            $this->traverse_element_to_extract_strings( $element );
            $this->traverse_element_to_extract_attribute_strings( $element );
        }

        // Then traverse the img elements to extract strings from the alt and title attributes
        $elements = $dom->findMulti( 'img' );
        foreach ( $elements as $element ) {
            $this->traverse_element_to_extract_attribute_strings( $element );
        }
        return \array_unique( $this->extracted_strings );
    }

    /**
     * Replace content strings with their translations
     *
     * @param string              $content The original HTML content
     * @param array<string,mixed> $search_replace_pairs Array of search/replace pairs
     * @return string The HTML content with replacements
     */
    public function replace_strings( string $content, array $search_replace_pairs ): string {
        if ( array() === $search_replace_pairs ) {
            return $content;
        }

        // Order the search/replace pairs by length of the search string so we can replace the longest strings first.
        $search_replace_pairs = $this->order_search_replace_pairs( $search_replace_pairs );

        // Get the allowed elements to replace.
        $dom      = HtmlDomParser::str_get_html( $content );
        $elements = $dom->find( '*' );

        // Traverse the elements and replace the strings with the translated versions.
        foreach ( $elements as $element ) {
            $this->traverse_element_to_replace_strings( $element, $search_replace_pairs );
            $this->traverse_element_to_replace_attribute_strings( $element, $search_replace_pairs );
        }

        // Then traverse the img elements to replace the strings in the alt and title attributes
        $elements = $dom->findMulti( 'img' );
        foreach ( $elements as $element ) {
            $this->traverse_element_to_replace_attribute_strings( $element, $search_replace_pairs );
        }
        return $dom->save();
    }

    /**
     * Traverse the element to extract strings
     *
     * @param SimpleHtmlDomInterface $element The element to traverse
     * @return void
     */
    private function traverse_element_to_extract_strings( SimpleHtmlDomInterface $element ): void {
        if ( $this->element_has_children( $element ) ) {
            foreach ( $element->children() as $child ) {
                $this->traverse_element_to_extract_strings( $child );
            }
        } elseif ( $this->is_text_node( $element ) && ! $this->is_excluded_tag( $element->tagName ) ) {
            $string = \trim( $element->textContent );
            if ( $this->is_valid_string( $string ) ) {
                $this->extracted_strings[] = $string;
            }
        }
    }

    /**
     * Traverse the element to replace strings
     *
     * @param SimpleHtmlDomInterface $element The element to traverse
     * @param array<string,string>   $search_replace_pairs The search/replace pairs
     * @return void
     */
    private function traverse_element_to_replace_strings( SimpleHtmlDomInterface $element, array $search_replace_pairs ): void {
        if ( $this->element_has_children( $element ) ) {
            foreach ( $element->children() as $child ) {
                $this->traverse_element_to_replace_strings( $child, $search_replace_pairs );
            }
        } elseif ( $this->is_text_node( $element ) && ! $this->is_excluded_tag( $element->tagName ) ) {
            $replacement = $this->find_replacement_for_string( $element->textContent, $search_replace_pairs );
            if ( '' !== $replacement ) {
                $replacement                                        = $this->replace_string(
                    $element->textContent,
                    $replacement,
                );
                $this->string_replacements[ $element->textContent ] = $replacement;
                $element->textContent                               = $replacement;
            }
        }
    }

    /**
     * Traverse the element to extract strings from attributes
     *
     * @param SimpleHtmlDomInterface $element The element to traverse
     * @return void
     */
    private function traverse_element_to_extract_attribute_strings( SimpleHtmlDomInterface $element ): void {
        if ( $this->element_has_children( $element ) ) {
            foreach ( $element->children() as $child ) {
                $this->traverse_element_to_extract_attribute_strings( $child );
            }
        } elseif ( $this->is_element_node( $element ) ) {
            foreach ( $this->get_allowed_attributes() as $attribute_name ) {
                if ( ! $element->hasAttribute( $attribute_name ) ) {
                    continue;
                }
                $attribute = $element->getAttribute( $attribute_name );
                if ( ! $this->is_valid_string( $attribute ) ) {
                    continue;
                }
                $this->extracted_strings[] = $attribute;
            }
        }
    }

    /**
     * Traverse the element to replace strings in attributes
     *
     * @param SimpleHtmlDomInterface $element The element to traverse
     * @param array<string,string>   $search_replace_pairs The search/replace pairs
     * @return void
     */
    private function traverse_element_to_replace_attribute_strings( SimpleHtmlDomInterface $element, array $search_replace_pairs ): void {
        if ( $this->element_has_children( $element ) ) {
            foreach ( $element->children() as $child ) {
                $this->traverse_element_to_replace_attribute_strings( $child, $search_replace_pairs );
            }
        } elseif ( $this->is_element_node( $element ) ) {
            foreach ( $this->get_allowed_attributes() as $attribute_name ) {
                if ( ! $element->hasAttribute( $attribute_name ) ) {
                    continue;
                }
                $attribute   = $element->getAttribute( $attribute_name );
                $replacement = $this->find_replacement_for_string( $attribute, $search_replace_pairs );
                if ( '' === $replacement ) {
                    continue;
                }
                $replacement                                       = $this->replace_string(
                    $attribute,
                    $replacement,
                );
                $this->attribute_string_replacements[ $attribute ] = $replacement;
                $element->setAttribute( $attribute_name, $replacement );
            }
        }
    }

    private function element_has_children( SimpleHtmlDomInterface $element ): bool {
        return $element->childElementCount && $element->childElementCount > 0;
    }

    /**
     * Check if the element is a text node or an element node
     *
     * @param SimpleHtmlDomInterface $element The element to check.
     * @return bool True if the element is a text node or an element node, false otherwise.
     * From both nodes we can get the text from the node->textContent property.
     */
    private function is_text_node( SimpleHtmlDomInterface $element ): bool {
        return $element->nodeType && ( self::NODE_TYPE_TEXT === $element->nodeType ||
            self::NODE_TYPE_ELEMENT === $element->nodeType );
    }

    /**
     * Check if the element is an element node
     *
     * @param SimpleHtmlDomInterface $element The element to check.
     * @return bool True if the element is an element node, false otherwise.
     */
    private function is_element_node( SimpleHtmlDomInterface $element ): bool {
        return $element->nodeType && self::NODE_TYPE_ELEMENT === $element->nodeType;
    }

    /**
     * Check if the element is an allowed tag
     *
     * @param string $tag_name The tag name to check.
     * @return bool True if the element is an allowed tag, false otherwise.
     */
    private function is_allowed_tag( string $tag_name ): bool {
        return \in_array( $tag_name, $this->get_allowed_tags(), true );
    }

    /**
     * Check if the element is an excluded tag
     *
     * @param string $tag_name The tag name to check.
     * @return bool True if the element is an excluded tag, false otherwise.
     */
    private function is_excluded_tag( string $tag_name ): bool {
        return \in_array( $tag_name, $this->get_excluded_tags(), true );
    }

    /**
     * Find the replacement for a string from the search/replace pairs
     *
     * @param string              $string The string to find the replacement for
     * @param array<string,mixed> $search_replace_pairs The search/replace pairs
     * @return string The replacement string
     */
    private function find_replacement_for_string( string $string, array $search_replace_pairs ): string {
        foreach ( $search_replace_pairs as $pair ) {
            if ( \trim( $pair['search'] ) === \trim( $string ) ) {
                return $pair['replace'];
            }
        }
        return '';
    }

    /**
     * Orders the search/replace pairs by length of the search string
     *
     * @param array<string,mixed> $search_replace_pairs The search/replace pairs
     * @return array<string,mixed> Ordered search/replace pairs
     */
    private function order_search_replace_pairs( array $search_replace_pairs ): array {
        \usort(
            $search_replace_pairs,
            static fn( $a, $b ) => \strlen( $b['search'] ) - \strlen( $a['search'] ),
        );
        return $search_replace_pairs;
    }
}
