<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

/**
 * Abstract class for handling translation functionality common to both posts and terms
 */
abstract class Abstract_Single_Translation_Handler {
    protected $language_manager;

    protected function __construct() {
        $this->language_manager = \xwp_app( 'pllat' )->get( Language_Manager::class );
    }

    /**
     * Enqueues admin scripts for the translation UI
     *
     * @return void
     */
    protected function enqueue_translation_admin_scripts() {
        \wp_enqueue_script(
            'pllat_single_translation',
            PLLAT_PLUGIN_URL . 'dist/js/build/single-translator.js',
            array( 'jquery' ),
            PLLAT_PLUGIN_VERSION,
            true,
        );

        \wp_localize_script(
            'pllat_single_translation',
            'pllat',
            array(
                'ajax_url'     => \admin_url( 'admin-ajax.php' ),
                'context'      => $this->get_translation_context(),
                'languages'    => $this->language_manager->get_language_list_for_ui(),
                'log_file_url' => \admin_url( 'admin.php?page=polylang-ai-automatic-translation&tab=support' ),
                'nonce'        => \wp_create_nonce( 'pllat_admin_nonce' ),
                'strings'      => $this->get_translation_strings(),
            ),
        );
    }

    /**
     * Gets the translation context (can be overridden by child classes)
     *
     * @return string 'post' or 'term' depending on context
     */
    protected function get_translation_context() {
        return 'generic';
    }

    /**
     * Gets translation strings for the UI
     *
     * @return array Translation strings
     */
    protected function get_translation_strings() {
        return array(
            'confirm'             => \__( 'Are you sure you want to generate/update translations?' ),
            'confirmCancel'       => \__(
                'Are you sure you want to cancel the translation process? All pending translations will be cancelled.',
            ),
            'currentlyScheduling' => \__( 'Scheduling %s version...' ),
            'deselectAll'         => \__( 'Deselect all' ),
            'dismiss'             => \__( 'Dismiss' ),
            'error'               => \__( 'Something went wrong. Please try again.' ),
            'errorCancelling'     => \__( 'An error occurred while cancelling the translation process.' ),
            'errorOccurred'       => \__( 'An error occurred while processing the %1$s translation. "%2$s"' ),
            'loading'             => \__( 'Loading...' ),
            'noLanguagesSelected' => \__( 'Select at least one language to generate translations.' ),
            'noTranslations'      => \__( 'No changes to translate.' ),
            'selectAll'           => \__( 'Select all' ),
            'status'              => array(
                'currentlyTranslating' => \__( 'In progress' ),
                'inProgress'           => \__( 'AI is translating in the background...' ),
                'inQueue'              => \__( 'Next up' ),
                'scheduling'           => \__( 'Scheduling' ),
                'waiting'              => \__( 'Analyzing and preparing content for translation...' ),
            ),
            'success'             => \__( 'Translations generated/updated successfully.' ),
        );
    }
}
