<?php //phpcs:disable SlevomatCodingStandard.Classes.SuperfluousExceptionNaming.SuperfluousSuffix
/**
 * String_List_Translation_Exception class file.
 *
 * @package Polylang Automatic AI Translation
 * @subpackage Exceptions
 */

namespace EPIC_WP\Polylang_Automatic_AI_Translation\Exceptions;

/**
 * Exception thrown when there is an error translating a list of strings
 */
class String_List_Translation_Exception extends \Exception {
    /**
     * List of original strings
     *
     * @var array<string>
     */
    private array $original_strings = array();

    /**
     * List of translated strings
     *
     * @var array<string>
     */
    private array $translated_strings = array();

    /**
     * Set original strings
     *
     * @param  array<string> $original_strings Original strings.
     */
    public function set_original_strings( array $original_strings ): void {
        $this->original_strings = $original_strings;
    }

    /**
     * Set translated strings
     *
     * @param  array<string> $translated_strings Translated strings.
     */
    public function set_translated_strings( array $translated_strings ): void {
        $this->translated_strings = $translated_strings;
    }

    /**
     * Get original strings
     *
     * @return array<string>
     */
    public function get_original_strings(): array {
        return $this->original_strings;
    }

    /**
     * Get translated strings
     *
     * @return array<string>
     */
    public function get_translated_strings(): array {
        return $this->translated_strings;
    }
}
