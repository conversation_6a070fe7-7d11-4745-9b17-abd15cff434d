<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator;

use EPIC_WP\Polylang_Automatic_AI_Translation\Array_Chunker;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Product_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Translation;
use GuzzleHttp\Pool;

class LLM_Translator implements Translator {
    private $api_client;
    private $response_handler;
    private $prompt_handler;
    private $token_encoder;
    private $max_tokens;
    private $max_prompt_tokens;

    public function __construct( API_Client_Interface $api_client, Response_Handler_Interface $response_handler, Prompt_Handler $prompt_handler, Token_Encoder_Interface $token_encoder ) {
        $this->api_client       = $api_client;
        $this->response_handler = $response_handler;
        $this->prompt_handler   = $prompt_handler;
        $this->token_encoder    = $token_encoder;
        $this->set_max_tokens( $api_client->get_max_tokens() );
    }

    public function translate_post( string $source_language, string $target_language, array $translation_input, array $placeholders = array() ): Post_Translation {
        $placeholders = array(
            ...$placeholders,
            'content_type' => 'post',
            'site_type'    => 'website',
        );
        return $this->translate(
            new Post_Translation(),
            $source_language,
            $target_language,
            $translation_input,
            $placeholders,
        );
    }

    public function translate_product( string $source_language, string $target_language, array $translation_input, array $placeholders = array() ): Product_Translation {
        $placeholders = array(
            ...$placeholders,
            'content_type'   => 'post meta',
            'site_type'      => 'website',
            'wordpress_type' => 'WooCommerce',
        );
        return $this->translate(
            new Product_Translation(),
            $source_language,
            $target_language,
            $translation_input,
            $placeholders,
        );
    }

    public function translate_post_meta( string $source_language, string $target_language, array $translation_input, array $placeholders = array() ): Post_Meta_Translation {
        $placeholders = array(
            ...$placeholders,
            'content_type' => 'post meta',
            'site_type'    => 'website',
        );
        return $this->translate(
            new Post_Meta_Translation(),
            $source_language,
            $target_language,
            $translation_input,
            $placeholders,
        );
    }

    public function translate_term( string $source_language, string $target_language, array $translation_input, array $placeholders = array() ): Term_Translation {
        $placeholders = array(
            ...$placeholders,
            'content_type' => 'term',
            'site_type'    => 'website',
        );
        return $this->translate(
            new Term_Translation(),
            $source_language,
            $target_language,
            $translation_input,
            $placeholders,
        );
    }

    public function translate_term_meta( string $source_language, string $target_language, array $translation_input, array $placeholders = array() ): Term_Meta_Translation {
        $placeholders = array(
            ...$placeholders,
            'content_type' => 'term meta',
            'site_type'    => 'website',
        );
        return $this->translate(
            new Term_Meta_Translation(),
            $source_language,
            $target_language,
            $translation_input,
            $placeholders,
        );
    }

    private function translate( Translation $translation, string $source_language, string $target_language, array $translation_input = array(), array $placeholders = array() ): Translation {
        if ( empty( $translation_input ) ) {
            return $translation;
        }

        try {

            $placeholders     = $this->prepare_placeholders(
                $source_language,
                $target_language,
                $placeholders,
            );
            $to_be_translated = $this->prepare_translation( $translation_input, $placeholders );

            if ( ! empty( $to_be_translated['large'] ) ) {
                foreach ( $to_be_translated['large'] as $field => $content ) {
                    $translation_result = $this->translate_large_content( $content, $placeholders );
                    $translation->set_field( $field, $translation_result );
                }
            }

            if ( ! empty( $to_be_translated['combined'] ) ) {
                foreach ( $to_be_translated['combined'] as $batch_number => $fields ) {
                    $translation_result = $this->translate_combined_content( $fields, $placeholders );
                    if ( empty( $translation_result ) ) {
                        continue;
                    }
                    foreach ( $translation_result as $field => $result ) {
                        $translation->set_field( $field, $result );
                    }
                }
            }

            return $translation;

        } catch ( \EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Exception $e ) {
            throw new \EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translation_API_Exception(
                'Error translating content via Translator API: ' . $e->getMessage(),
                $e->getCode(),
                $e,
            );
        }
    }

    public function translate_combined_content( array $fields, array $placeholders ): array {
        $placeholders['content'] = Helpers::encode_json( $fields );
        $messages                = $this->prompt_handler->get_prompt_messages(
            'translation',
            $placeholders,
            $this->api_client->is_reasoning_model(),
        );
        $response                = $this->api_client->complete( $messages, true );
        $translated_fields       = $this->response_handler->handle_response( $response, true, true );
        return $translated_fields;
    }

    public function translate_large_content( string $content, array $placeholders ): string {
        $large_content_translator = new Large_Content_Translator(
            $this->api_client,
            $this->response_handler,
            $this->prompt_handler,
            $this->token_encoder,
        );
        return $large_content_translator->translate( $content, $placeholders );
    }

    public function translate_hashed_strings_list( array $hashed_strings, array $placeholders ): array {
        // Chunk the hashed strings list into chunks smaller than the max prompt tokens
        $array_chunker = new Array_Chunker( $hashed_strings, $this->token_encoder );
        $chunks        = $array_chunker->chunk( $this->max_prompt_tokens );

        $translated_chunks = array();
        $failed_chunks     = array();
        $max_retries       = 2;

        // First attempt at translating all chunks
        $this->process_chunks( $chunks, $placeholders, $translated_chunks, $failed_chunks );

        // Retry failed chunks up to max_retries times
        $retry_count = 1;
        while ( ! empty( $failed_chunks ) && $retry_count <= $max_retries ) {
            Helpers::log(
                'Retrying ' . \count(
                    $failed_chunks,
                ) . " failed chunks (attempt $retry_count of $max_retries)",
                'info',
            );

            $retry_chunks = array();
            foreach ( $failed_chunks as $index => $chunk ) {
                $retry_chunks[ $index ] = $chunk;
            }

            // Reset failed chunks for this retry attempt
            $failed_chunks = array();

            // Process the failed chunks again
            $this->process_chunks( $retry_chunks, $placeholders, $translated_chunks, $failed_chunks );

            ++$retry_count;
        }

        // If any chunks still failed after all retries, throw an exception
        if ( ! empty( $failed_chunks ) ) {
            $failed_indices = \implode( ', ', \array_keys( $failed_chunks ) );
            throw new \Exception(
                "Translation failed for chunks: $failed_indices after $max_retries retry attempts.",
            );
        }

        // Ensure all chunks were successfully translated
        if ( \count( $translated_chunks ) !== \count( $chunks ) ) {
            throw new \Exception(
                'Incomplete translation: Only ' . \count( $translated_chunks ) . ' of ' . \count(
                    $chunks,
                ) . ' chunks were translated.',
            );
        }

        // Merge the translated chunks into a single array, keeping the original hash keys
        $translated_strings = array();
        if ( ! empty( $translated_chunks ) ) {
            foreach ( $translated_chunks as $translated_chunk ) {
                $translated_strings += $translated_chunk;
            }
        }

        return $translated_strings;
    }

    /**
     * Process chunks of translations through the API
     *
     * @param array $chunks Array of chunks to translate
     * @param array $placeholders Placeholders for translations
     * @param array &$translated_chunks Reference to array to store successful translations
     * @param array &$failed_chunks Reference to array to store failed chunks
     */
    private function process_chunks( $chunks, $placeholders, &$translated_chunks, &$failed_chunks ) {
        $requests = function ( $chunks ) use ( $placeholders ) {
            foreach ( $chunks as $index => $chunk ) {
                $placeholders['content'] = \wp_json_encode(
                    $chunk['strings'],
                    JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE,
                );
                $messages                = $this->prompt_handler->get_prompt_messages(
                    'hashed-strings-list-translation',
                    $placeholders,
                );
                yield $index => fn() => $this->api_client->async_complete( $messages, true );
            }
        };

        // Use Pool to handle concurrent requests
        $pool = new Pool(
            $this->api_client->get_api_client(),
            $requests( $chunks ),
            array(
                'concurrency' => 1,
                'fulfilled'   => function ( $response, $index ) use ( &$translated_chunks, &$failed_chunks, $chunks ) {
                    $result = $this->response_handler->handle_response( $response, true, true );
                    if ( ! empty( $result ) ) {
                        $translated_chunks[ $index ] = $result;
                    } else {
                        // Consider empty results as failures
                        Helpers::log( "Translation produced empty result for chunk $index", 'error' );
                        $failed_chunks[ $index ] = $chunks[ $index ];
                    }
                },
                'rejected'    => static function ( $reason, $index ) use ( &$failed_chunks, $chunks ) {
                    Helpers::log( "Translation failed for chunk $index: " . $reason->getMessage(), 'error' );
                    $failed_chunks[ $index ] = $chunks[ $index ];
                },
            ),
        );

        // Wait for the pool to complete
        $promise = $pool->promise();
        $promise->wait();
    }

    public function prepare_translation( array $translation_input = array(), array $placeholders = array() ) {
        $combined_batches      = array();
        $combined_batch_number = 0;
        $combined_token_count  = 0;
        $large_fields          = array();

        foreach ( $translation_input as $field => $content ) {

            // Count the tokens of the content of the field with tiktoken
            $token_count = $this->count_tokens( $content );

            // If the field is too large, add it to the large fields array
            if ( $token_count > $this->max_prompt_tokens || 'post_content' === $field ) {
                $large_fields[ $field ] = $content;
                continue;
            }

            // If the token count of the combined fields is less than the max prompt tokens, add the field to this batch of combined fields
            if ( $token_count + $combined_token_count < $this->max_prompt_tokens ) {
                $combined_token_count += $token_count;
            }
            // Otherwise, start a new batch of combined fields and reset the token count
            else {
                $combined_token_count = $token_count;
                ++$combined_batch_number;
            }

            // Add the field to the combined fields batches
            $combined_batches[ $combined_batch_number ][ $field ] = $content;
        }

        return array(
            'combined' => $combined_batches,
            'large'    => $large_fields,
        );
    }

    public function count_tokens( string $content ) {
        return $this->token_encoder->count_tokens( $content );
    }

    public function set_max_tokens( int $max_tokens ) {
        $this->api_client->set_max_tokens( $max_tokens );
        $this->max_tokens        = $max_tokens;
        $this->max_prompt_tokens = $this->max_tokens / 2;
    }

    private function prepare_placeholders( string $source_language, string $target_language, array $additional_placeholders = array() ): array {
        $placeholders = \apply_filters(
            'pllat_translation_placeholders',
            array(
                'source_language' => \strtoupper( $source_language ),
                'target_language' => \strtoupper( $target_language ),
                'wordpress_type'  => 'WordPress',
            ),
            $source_language,
            $target_language,
            $additional_placeholders,
        );
        return \array_merge( $placeholders, $additional_placeholders );
    }
}
