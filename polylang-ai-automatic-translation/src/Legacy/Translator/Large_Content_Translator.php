<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator;

use EPIC_WP\Polylang_Automatic_AI_Translation\Content_String_Editor\HTML_Content_String_Editor;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use EPIC_WP\Polylang_Automatic_AI_Translation\Text_Chunker;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator_Factory;

class Large_Content_Translator {
    private $max_tokens;
    private $max_prompt_tokens;
    private $content_editor;

    public function __construct( API_Client_Interface $api_client, Response_Handler_Interface $response_handler, Prompt_Handler $prompt_handler, Token_Encoder_Interface $token_encoder ) {
        $this->api_client        = $api_client;
        $this->response_handler  = $response_handler;
        $this->prompt_handler    = $prompt_handler;
        $this->token_encoder     = $token_encoder;
        $this->content_chunker   = new Text_Chunker( $token_encoder );
        $this->content_editor    = new HTML_Content_String_Editor();
        $this->max_tokens        = $api_client->get_max_tokens();
        $this->max_prompt_tokens = $this->max_tokens / 2;
    }

    public function translate( string $content, array $placeholders ): string {
        // Extract all strings from the html content
        $extracted_strings = $this->content_editor->extract_strings( $content );

        // Extract all relevant strings from the content
        $all_strings = \apply_filters(
            'pllat_large_content_translation_relevant_strings',
            $extracted_strings,
            $content,
        );

        // Hash all the strings
        $hashed_source_strings = $this->hash_strings( $all_strings );

        // Translate all the hashed strings
        $translator                = Translator_Factory::create_translator(
            Settings::get_active_translation_api(),
        );
        $translated_hashed_strings = $translator->translate_hashed_strings_list(
            $hashed_source_strings,
            $placeholders,
        );

        // Create search/replace pairs from translations
        $search_replace_pairs = array();
        foreach ( $hashed_source_strings as $hash => $source_string ) {
            if ( empty( $translated_hashed_strings[ $hash ] ) ) {
                continue;
            }

            $search_replace_pairs[] = array(
                'replace' => $translated_hashed_strings[ $hash ],
                'search'  => $source_string,
            );
        }

        // Hook into the content before strings are replaced
        $content = \apply_filters(
            'pllat_content_after_large_content_translation',
            $content,
            $search_replace_pairs,
        );

        // Filter the search/replace pairs to only include pairs where the search string also exists in the initial extracted html strings.
        $search_replace_pairs = $this->filter_search_replace_pairs( $search_replace_pairs, $extracted_strings );

        // Replace the source strings with their corresponding translations
        $content = $this->content_editor->replace_strings( $content, $search_replace_pairs );

        return $content;
    }

    /**
     * Filters the search/replace pairs to only include pairs where the search string also exists in the initial extracted html strings.
     *
     * @param array $search_replace_pairs The search/replace pairs to filter
     * @param array $extracted_strings The initial extracted html strings
     * @return array The filtered search/replace pairs
     */
    private function filter_search_replace_pairs( array $search_replace_pairs, array $extracted_strings ): array {
        return \array_filter(
            $search_replace_pairs,
            static fn( $pair ) => \in_array( $pair['search'], $extracted_strings ),
        );
    }

    /**
     * Hashes each string in the array to a 8 character long hash string
     *
     * @param array $strings The strings to hash
     * @return array The hashed strings
     */
    private function hash_strings( array $strings ): array {
        $hashed_strings = array();
        foreach ( $strings as $index => $string ) {
            $hash                    = \substr( \md5( $string . $index ), 0, 8 );
            $hashed_strings[ $hash ] = $string;
        }
        return $hashed_strings;
    }

    public function set_max_tokens( int $max_tokens ): void {
        $this->max_tokens = $max_tokens;
    }
}
