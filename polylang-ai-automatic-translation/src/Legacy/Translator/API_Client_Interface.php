<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator;

use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Promise\Promise;
use GuzzleHttp\Client;

interface API_Client_Interface {
    public function complete(array $messages): Response;
    public function async_complete(array $messages): Promise;
    public function get_api_client(): Client;
    public function is_reasoning_model(): bool;
    public function get_max_tokens(): int;
}