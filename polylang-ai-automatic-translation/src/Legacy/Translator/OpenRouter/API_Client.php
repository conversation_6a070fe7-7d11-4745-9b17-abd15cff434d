<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator\OpenRouter;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\API_Client_Interface;
use GuzzleHttp\Client;
use GuzzleHttp\Promise\Promise;
use GuzzleHttp\Psr7\Response;

class API_Client implements API_Client_Interface {
    private $api_key;
    private $model;
    private $client;
    private $max_tokens;

    public function __construct( string $api_key, string $model ) {
        $this->api_key    = $api_key;
        $this->model      = $model;
        $this->client     = $this->get_api_client();
        $this->max_tokens = 5000;
    }

    public function complete( array $messages = array(), bool $json_output = false ): Response {
        $body = array(
            'json' => array(
                'messages'        => $messages,
                'model'           => $this->model,
                'response_format' => array( 'type' => 'json_object' ),
                'stream'          => false,
            ),
        );

        try {
            $response = $this->client->post( 'chat/completions', $body );
            return $response;
        } catch ( \Exception $e ) {
            throw $this->handle_exception( $e );
        }
    }

    public function async_complete( array $messages = array(), bool $json_output = false ): Promise {
        $body = array(
            'json' => array(
                'max_tokens'      => $this->max_tokens,
                'messages'        => $messages,
                'model'           => $this->model,
                'response_format' => array( 'type' => 'json_object' ),
                'stream'          => false,
            ),
        );

        return $this->client->postAsync( 'chat/completions', $body )->then(
            static fn( Response $response ) => $response,
            function ( \Exception $e ) {
                throw $this->handle_exception( $e );
            },
        );
    }

    private function handle_exception( \Exception $e ): \Exception {
        if ( $e instanceof \GuzzleHttp\Exception\RequestException && $e->hasResponse() ) {
            $response_body = \json_decode( $e->getResponse()->getBody(), true );
            $error_message = $response_body['error']['message'] ?? 'Unknown error occurred';
        } else {
            $error_message = $e->getMessage();
        }
        return new \Exception( 'Request error while making an async API request: ' . $error_message, 0, $e );
    }

    public function get_api_client(): Client {
        return new Client(
            array(
                'base_uri' => 'https://openrouter.ai/api/v1/',
                'headers'  => array(
                    'Authorization' => 'Bearer ' . $this->api_key,
                    'Content-Type'  => 'application/json',
                    'HTTP-Referer'  => \get_site_url(), // Required by OpenRouter
                    'X-Title'       => 'Polylang AI Automatic Translation', // Required by OpenRouter
                ),
                'timeout'  => 180,
            ),
        );
    }

    public function is_reasoning_model(): bool {
        return true;
    }

    public function get_max_tokens(): int {
        return $this->max_tokens;
    }

    public function set_max_tokens( int $max_tokens ): void {
        $this->max_tokens = $max_tokens;
    }
}
