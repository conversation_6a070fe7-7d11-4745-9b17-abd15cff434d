<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator\OpenRouter;

use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Response_Handler_Interface;

class Response_Handler implements Response_Handler_Interface {

    /**
     * Handle the API response and extract the content
     *
     * @param mixed $response The API response object
     * @param bool $json_output Whether to parse the content as JSON
     * @param bool $correct_nesting Whether to correct nested arrays in the JSON
     * @return string|array The extracted content
     * @throws \Exception If the response is invalid or contains an error
     */
    public function handle_response($response, bool $json_output = false, bool $correct_nesting = false) {

        // Get and log the raw response
        $response_body = $this->get_response_body($response);
        
        // Parse the outer response structure
        $result = $this->parse_api_response($response_body);
        
        // Check for API errors
        $this->check_for_api_errors($result);
        
        // Extract the content from the response
        $content = $this->extract_content($result);
        
        // Process the content based on the output type
        return $json_output 
            ? $this->handle_json_response($content, $correct_nesting) 
            : $content;
    }

    /**
     * Get and clean the response body
     *
     * @param mixed $response The API response object
     * @return string The cleaned response body
     */
    private function get_response_body($response): string {
        $response_body = trim($response->getBody()->getContents());
        return $response_body;
    }

    /**
     * Parse the API response into an array
     *
     * @param string $response_body The raw response body
     * @return array The parsed response
     * @throws \Exception If the response cannot be parsed
     */
    private function parse_api_response(string $response_body): array {
        try {
            $result = json_decode($response_body, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Failed to decode API response: ' . json_last_error_msg());
            }
            
            return $result;
        } catch (\Exception $e) {
            error_log('Failed to decode API response body: ' . $e->getMessage());
            throw new \Exception('Invalid response format from OpenRouter API');
        }
    }

    /**
     * Check for API errors in the response
     *
     * @param array $result The parsed API response
     * @throws \Exception If the response contains an error
     */
    private function check_for_api_errors(array $result): void {
        if (isset($result['error']['message'])) {
            throw new \Exception('OpenRouter API error: ' . $result['error']['message']);
        }
    }

    /**
     * Extract the content from the API response
     *
     * @param array $result The parsed API response
     * @return string The extracted content
     * @throws \Exception If the content is empty
     */
    private function extract_content(array $result): string {
        $content = $result['choices'][0]['message']['content'] ?? null;

        if (empty($content)) {
            throw new \Exception('OpenRouter API completion does not contain content.');
        }

        // Clean up any formatted JSON by removing newlines and unnecessary whitespace
        // Only do this cleanup if the content looks like JSON (starts with [ or {)
        if (preg_match('/^\s*[\[\{]/', $content)) {
            // Replace newlines and excessive whitespace in JSON while preserving valid JSON structure
            $content = preg_replace('/\s*\n\s*/', ' ', $content);
            $content = preg_replace('/\s+(?=(?:[^"]*"[^"]*")*[^"]*$)/', ' ', $content);
            
            // Remove outer square brackets if it's a JSON array with a single object
            // Pattern matches: [{ ... }] with optional whitespace
            if (preg_match('/^\s*\[\s*\{.*\}\s*\]\s*$/s', $content)) {
                $content = preg_replace('/^\s*\[\s*(\{.*\})\s*\]\s*$/s', '$1', $content);
            }
        }

        return trim($content);
    }

    /**
     * Handle JSON response content
     *
     * @param string $completion The JSON string to parse
     * @param bool $correct_nesting Whether to correct nested arrays
     * @return array The parsed JSON
     * @throws \Exception If the JSON cannot be parsed
     */
    private function handle_json_response(string $completion, bool $correct_nesting = false): array {
        // Clean up the completion string
        $completion = trim($completion);
        
        if (empty($completion)) {
            throw new \Exception('Empty completion received from OpenRouter API');
        }

        try {
            // Try different JSON decoding strategies
            $decoded = $this->try_decode_json($completion);
            
            // Apply nesting correction if needed
            if ($correct_nesting) {
                $decoded = $this->correct_nested_arrays($decoded);
            }
                        
            return $decoded;
        } catch (\Exception $e) {
            $this->log_json_error($e, $completion);
            throw new \Exception('Failed to decode JSON response from OpenRouter API: ' . $e->getMessage());
        }
    }

    /**
     * Try different strategies to decode JSON
     *
     * @param string $json_string The JSON string to decode
     * @return array The decoded JSON
     * @throws \Exception If all decoding attempts fail
     */
    private function try_decode_json(string $json_string): array {
        
        // Strategy 0: Strip markdown code block formatting
        if (preg_match('/^```(?:json)?\s*([\s\S]+?)```\s*$/m', $json_string, $matches)) {
            $json_string = $matches[1];
        }
        
        // Strategy 1: Direct decode
        $decoded = json_decode($json_string, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }
        
        // Strategy 2: Clean control characters
        $cleaned = preg_replace('/[\x00-\x1F\x7F]/', '', $json_string);
        $decoded = json_decode($cleaned, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }

        // Strategy 3: Fix unescaped quotes in HTML or text content
        // This makes the JSON valid by escaping quotes that should be escaped
        $fixed = preg_replace('/:(\s*)"([^"]*?)(<[^>]*>)([^<]*?)"([^<]*?)(<\/[^>]*>)([^"]*?)"/', ':$1"$2$3$4\\"$5$6$7"', $cleaned);
        $decoded = json_decode($fixed, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }

        // Strategy 4: Handle double-escaped quotes (only if necessary)
        $fixed = str_replace('\\"', '"', $cleaned);
        $decoded = json_decode($fixed, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }

        // If all strategies fail, throw an error
        throw new \Exception('JSON decode error: ' . json_last_error_msg() . ' JSON string: ' . $json_string);

    }
    
    /**
     * Correct nested arrays in the decoded JSON
     *
     * @param array $decoded The decoded JSON
     * @return array The corrected array
     */
    private function correct_nested_arrays(array $decoded): array {
        return array_map(function($value) {
            return is_array($value) ? reset($value) : $value;
        }, $decoded);
    }

    /**
     * Log JSON decoding errors
     *
     * @param \Exception $exception The exception that occurred
     * @param string $completion The JSON string that failed to decode
     */
    private function log_json_error(\Exception $exception, string $completion): void {
        error_log('JSON decode error in OpenRouter response handler: ' . $exception->getMessage());
        error_log('Completion content: ' . $completion);
    }
}