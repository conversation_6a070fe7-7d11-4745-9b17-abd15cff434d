<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Product_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Meta_Translation;

interface Translator {
    public function translate_post(string $source_language, string $target_language, array $translation_input, array $placeholders = []): Post_Translation;
    public function translate_product(string $source_language, string $target_language, array $translation_input, array $placeholders = []): Product_Translation;
    public function translate_post_meta(string $source_language, string $target_language, array $translation_input, array $placeholders = []): Post_Meta_Translation;
    public function translate_term(string $source_language, string $target_language, array $translation_input, array $placeholders = []): Term_Translation;
    public function translate_term_meta(string $source_language, string $target_language, array $translation_input, array $placeholders = []): Term_Meta_Translation;
}