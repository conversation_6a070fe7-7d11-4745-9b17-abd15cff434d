<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator;

use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;

class Translator_Factory {

    public static function create_translator(string $translator, $args = []): Translator {
        switch ($translator) {
            case 'openai' :
                return self::get_openai_translator($translator, $args);
            case 'openrouter' :
                $api_key = Settings::get_translation_api_key('openrouter');
                $model = Settings::get_translation_model('openrouter');
                $api_client = new OpenRouter\API_Client($api_key, $model);
                $response_handler = new OpenRouter\Response_Handler();
                $token_encoder = new OpenRouter\Token_Encoder($model);
                $prompt_handler = new Prompt_Handler($token_encoder);
                return new LLM_Translator($api_client, $response_handler, $prompt_handler, $token_encoder);
            default:
                return self::get_openai_translator($translator, $args);
        }
    }

    private static function get_openai_translator(string $translator, $args = []) {
        $api_key = Settings::get_translation_api_key('openai');
        $model = Settings::get_translation_model('openai');
        $api_client = new OpenAI\API_Client($api_key, $model);
        $response_handler = new OpenAI\Response_Handler();
        $token_encoder = new OpenAI\Token_Encoder($model);
        $prompt_handler = new Prompt_Handler($token_encoder);
        return new LLM_Translator($api_client, $response_handler, $prompt_handler, $token_encoder);
    }

}