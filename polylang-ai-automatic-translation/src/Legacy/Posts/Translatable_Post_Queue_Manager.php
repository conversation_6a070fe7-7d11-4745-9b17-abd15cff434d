<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Posts;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity_Queue_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity;

class Translatable_Post_Queue_Manager extends Translatable_Entity_Queue_Manager {

    public function get_available_fields(array $args = []): array {
        return Post_Translation::get_available_fields_for($this->entity->get_id());
    }

    public function get_available_meta_fields(): array {
        return Post_Meta_Translation::get_available_fields_for($this->entity->get_id());
    }

    protected function update_queue_meta(array $queue) {
        update_post_meta($this->entity->get_id(), Translatable_Entity::META_KEY_QUEUE, $queue);
    }

    protected function delete_queue_meta() {
        delete_post_meta($this->entity->get_id(), Translatable_Entity::META_KEY_QUEUE);
    }
}