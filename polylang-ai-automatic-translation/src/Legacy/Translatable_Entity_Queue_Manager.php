<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;

abstract class Translatable_Entity_Queue_Manager {
    
    protected Translatable_Entity $entity;
    protected Language_Manager $language_manager;

    public function __construct(Translatable_Entity $entity, Language_Manager $language_manager) {
        $this->entity = $entity;
        $this->language_manager = $language_manager;
    }

    abstract public function get_available_fields(): array;
    abstract public function get_available_meta_fields(): array;
    abstract protected function update_queue_meta(array $queue);
    abstract protected function delete_queue_meta();

    public function populate_queue(array $languages = []) {

        $all_allowed_fields = $this->get_available_fields();
        $all_allowed_meta_fields = $this->get_available_meta_fields();
        
        if (!empty($languages)) {
            foreach ($languages as $language) {
                $this->add_to_queue($language, $all_allowed_fields);
                $this->add_meta_to_queue($language, $all_allowed_meta_fields);
            }
        } else {
            $missing_languages = $this->entity->get_missing_translations();
            $unprocessed_languages = $this->entity->get_unprocessed_translations();
            
            if (!empty($missing_languages)) {
                foreach ($missing_languages as $language) {
                    $this->add_to_queue($language, $all_allowed_fields);
                    $this->add_meta_to_queue($language, $all_allowed_meta_fields);
                }
            }
            
            if (!empty($unprocessed_languages)) {
                foreach ($unprocessed_languages as $language) {
                    $this->add_to_queue($language, $all_allowed_fields);
                    $this->add_meta_to_queue($language, $all_allowed_meta_fields);
                }
            }
        }

        do_action('pllat_after_populate_queue', $this->entity->get_id(), $languages);
    }

    public function tidy_queue() {
        $queue = $this->entity->get_queue();
        $queue_languages = array_keys($queue);
        if (empty($queue_languages)) return;
        $available_languages = $this->entity->get_available_languages();
        $current_language = $this->entity->get_language();
        foreach ($queue_languages as $language) {
            if (!in_array($language, $available_languages) || $language === $current_language) {
                unset($queue[$language]);
            }
        }
        $this->update_queue($queue);
    }

    public function add_to_queue(string $language, array $fields) {
        if ($language === $this->entity->get_language()) return;
        $queue = $this->entity->get_queue();
        $available_fields = $this->get_available_fields();
        if (!isset($queue[$language])) {
            $queue[$language] = [];
        }
        foreach ($fields as $field) {
            if (in_array($field, $available_fields) && !in_array($field, $queue[$language])) {
                $queue[$language][] = $field;
            }
        }
        $this->update_queue($queue);
    }

    public function remove_from_queue(string $language) {
        $queue = $this->entity->get_queue();
        unset($queue[$language]);
        $this->update_queue($queue);
    }

    public function add_meta_to_queue(string $language, array $meta_keys) {

        $queue_key = Translatable_Entity::QUEUE_META_FIELD_KEY;

        if ($language === $this->entity->get_language()) return;
        $queue = $this->entity->get_queue();
        $available_meta_fields = $this->get_available_meta_fields();
        if (!isset($queue[$language])) {
            $queue[$language] = [];
        }
        if (!isset($queue[$language][$queue_key])) {
            $queue[$language][$queue_key] = [];
        }
        $meta_queue = $queue[$language][$queue_key];
        foreach ($meta_keys as $meta_key) {
            if (in_array($meta_key, $available_meta_fields) && !in_array($meta_key, $meta_queue)) {
                $meta_queue[] = $meta_key;
            }
        }
        $queue[$language][$queue_key] = $meta_queue;
        $this->update_queue($queue);
    }

    public function remove_meta_from_queue(string $language) {
        $queue = $this->entity->get_queue();
        unset($queue[$language][Translatable_Entity::QUEUE_META_FIELD_KEY]);
        $this->update_queue($queue);
    }

    public function add_custom_data_to_queue(string $language, string $custom_data_key, array $custom_data) {

        $queue_key = Translatable_Entity::QUEUE_CUSTOM_DATA_FIELD_KEY;

        if ($language === $this->entity->get_language()) return;
        $queue = $this->entity->get_queue();
        if (!isset($queue[$language])) {
            $queue[$language] = [];
        }
        if (!isset($queue[$language][$queue_key])) {
            $queue[$language][$queue_key] = [];
        }
        $custom_queue = $queue[$language][$queue_key];
        $custom_queue[$custom_data_key] = array_unique(array_merge($custom_queue[$custom_data_key] ?? [], $custom_data));
        $queue[$language][$queue_key] = $custom_queue;
        $this->update_queue($queue);
    }

    public function remove_custom_data_from_queue(string $language, string $custom_data_key) {
        $queue = $this->entity->get_queue();
        unset($queue[$language][Translatable_Entity::QUEUE_CUSTOM_DATA_FIELD_KEY][$custom_data_key]);
        $this->update_queue($queue);
    }

    protected function update_queue(array $queue) {
        $filtered_queue = array_filter($queue, function($language_queue) {
            return !empty($language_queue);
        });
        if (empty($filtered_queue)) {
            $this->delete_queue_meta();
        } else {
            $this->update_queue_meta($filtered_queue);
        }
    }
}
