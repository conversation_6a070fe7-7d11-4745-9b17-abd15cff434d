<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Terms;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity;

class Translatable_Term extends Translatable_Entity {

    public function get_language(): string {
        return $this->language_manager->get_term_language($this->get_id());
    }

    public function get_translations(): array {
        return $this->language_manager->get_term_translations($this->get_id());
    }

    public function get_taxonomy(): ?string {
        $term = get_term($this->get_id());
        if ($term instanceof \WP_Term) {
            return $term->taxonomy;
        }
        return null;
    }

    protected function get_meta(string $key, bool $single = false) {
        return get_term_meta($this->get_id(), $key, $single);
    }

    protected function is_excluded_from_translation_meta(): bool {
        return get_term_meta($this->get_id(), self::META_KEY_EXCLUDE, true) == true;
    }

    protected function create_translatable_entity(int $id): self {
        return new self($id, $this->language_manager);
    }

    public function get_title(): string {
        return get_term($this->get_id())->name;
    }

    public function get_edit_link(): string {
        $link = get_edit_term_link($this->get_id());
        if ($link) {
            return $link;
        }
        // Fallback: construct the admin URL manually
        $taxonomy = $this->get_taxonomy();
        return admin_url(sprintf('term.php?tag_ID=%d&taxonomy=%s', $this->get_id(), $taxonomy));
    }
}
