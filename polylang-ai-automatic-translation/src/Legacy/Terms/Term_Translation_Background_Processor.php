<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Terms;

use EPIC_WP\Polylang_Automatic_AI_Translation\Action_Scheduler\Abstract_Single_Translation_Background_Processor;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;

class Term_Translation_Background_Processor extends Abstract_Single_Translation_Background_Processor {
    /**
     * Constructor
     */
    public function __construct() {
        $this->group = 'pllat-single-term-translation';
        $this->process_hook = 'pllat_process_single_term_translation';
        parent::__construct();
    }
    
    /**
     * Prepare arguments for the action
     * 
     * @param int $term_id The term ID
     * @param string $language_slug The language slug
     * @param array $args Additional arguments
     * @return array Prepared arguments
     */
    protected function prepare_action_args(int $term_id, string $language_slug, array $args): array {
        return [
            'term_id' => $term_id,
            'language_slug' => $language_slug,
            'args' => $args,
        ];
    }
    
    /**
     * Get the item ID key name for this scheduler
     * 
     * @return string The item ID key name
     */
    protected function get_item_id_key(): string {
        return 'term_id';
    }
    
    /**
     * Process the term using the term manager
     * 
     * @param int $term_id The term ID
     * @param string $language_slug The language slug
     * @param array $args Additional arguments
     * @return void
     */
    protected function process_item(int $term_id, string $language_slug, array $args): void {
        $term_manager = Helpers::get_translatable_term_manager($term_id);
        $term_manager->process_queue_item($language_slug, $args);
    }
    
    /**
     * Schedule a term translation (alias for schedule_translation to maintain backward compatibility)
     * 
     * @param int $term_id The term ID to translate
     * @param string $language_slug The language to translate to
     * @param array $args Additional arguments for translation
     * @return int|bool The scheduled action ID or false
     */
    public function schedule_term_translation(int $term_id, string $language_slug, array $args = []) {
        return $this->schedule_translation($term_id, $language_slug, $args);
    }
    
    /**
     * Handle a scheduled term translation (alias for handle_translation to maintain backward compatibility)
     * 
     * @param int $term_id The term ID
     * @param string $language_slug The language slug
     * @param array $args Additional arguments
     * @return void
     */
    public function handle_term_translation(int $term_id, string $language_slug, array $args = []): void {
        $this->handle_translation($term_id, $language_slug, $args);
    }
    
    /**
     * Get the translation status for a term (alias for get_translation_status to maintain backward compatibility)
     * 
     * @param int $term_id The term ID
     * @return array Status information
     */
    public function get_term_translation_status(int $term_id): array {
        return $this->get_translation_status($term_id);
    }
    
    /**
     * Cancel all scheduled translations for a specific term (alias for cancel_translations to maintain backward compatibility)
     * 
     * @param int $term_id The term ID
     * @return int The number of cancelled actions
     */
    public function cancel_term_translations(int $term_id): int {
        return $this->cancel_translations($term_id);
    }
    
    /**
     * Get the type of item this scheduler handles
     *
     * @return string The item type ('term')
     */
    protected function get_item_type(): string {
        return 'term';
    }
} 