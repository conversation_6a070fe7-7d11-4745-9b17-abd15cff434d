<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Models;

class Translation_Models {

    /**
     * Get OpenAI models configuration
     *
     * @return array
     */
    public static function get_openai_models(): array {
        return [
            'gpt-4o' => [
                'label' => pll__('GPT-4o (most used)'),
            ],
            'gpt-4o-mini' => [
                'label' => pll__('GPT-4o Mini'),
            ],
            'gpt-4.1-2025-04-14' => [
                'label' => pll__('GPT-4.1'),
            ],
            'gpt-4.1-mini-2025-04-14' => [
                'label' => pll__('GPT-4.1 Mini'),
            ],
        ];
    }

    /**
     * Get OpenRouter models configuration
     *
     * @return array
     */
    public static function get_openrouter_models(): array {
        return [
            'free' => [
                'google/gemini-2.0-flash-exp:free' => [
                    'label' => pll__('Gemini Flash (Google)'),
                ],
                'google/gemini-2.0-flash-lite-preview-02-05:free' => [
                    'label' => pll__('Gemini Flash Lite (Google)'),
                ],
                'google/gemini-2.0-pro-exp-02-05:free' => [
                    'label' => pll__('Gemini Pro (Google)'),
                ],
            ],
            'paid' => [
                'openai/gpt-4o-2024-11-20' => [
                    'label' => pll__('GPT-4o (OpenAI)'),
                ],
                'openai/gpt-4o-mini' => [
                    'label' => pll__('GPT-4o Mini (OpenAI)'),
                    'cost_per_1k_words' => 0,
                ],
                'anthropic/claude-3.5-sonnet' => [
                    'label' => pll__('Claude 3.5 Sonnet (Anthropic)'),
                ],
                'anthropic/claude-3.7-sonnet' => [
                    'label' => pll__('Claude 3.7 Sonnet (Anthropic)'),
                ],
                'deepseek/deepseek-chat-v3-0324' => [
                    'label' => pll__('DeepSeek V3 0324 (DeepSeek)'),
                ],
            ],
        ];
    }

    /**
     * Get cost tooltip for a specific API
     *
     * @param string $api_type
     * @return string
     */
    public static function get_tooltip(string $api_type): string {
        $tooltip = '';
        switch ($api_type) {
            case 'openai':
                $tooltip = pll__('Direct access to OpenAI API is on average cheaper than using OpenRouter, but you are limited by your account\'s rate limits. Read more here: https://platform.openai.com/docs/usage-policies/rate-limits');
                break;
                
            case 'openrouter':
                $tooltip = pll__('The paid AI models of OpenRouter are on average more expensive, but have access to the highest rate limits and timeout limits.');
                break;
        }
        
        return $tooltip;
    }

    /**
     * Get model description for a specific API
     *
     * @param string $api_type
     * @return string
     */
    public static function get_model_description(string $api_type): string {
        switch ($api_type) {
            case 'openai':
                return pll__('We support GPT-4o and GPT-4.1 models. GPT-4o provides the best balance of cost and speed, while GPT-4.1 offers enhanced reasoning capabilities for complex translations.');
            case 'openrouter':
                return pll__('OpenRouter is a unified API that gives you access to multiple AI models from different providers. You can choose between free and paid models - paid models like Claude 3.5 Sonnet provide excellent translation quality but have associated costs. Openrouter is <strong><u>more expensive</u></strong> because the platform adds an additional fee to the cost of the model.');
            default:
                return '';
        }
    }
} 