<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translations;

class Term_Meta_Translation extends Translation {

    public static function get_available_fields() : array {
        return apply_filters('pllat_available_term_meta_translation_fields', [
            // WordPress core
            'term_description',
            'category_image',
            
            // Yoast SEO 
            'wpseo_title',
            'wpseo_desc',
            'wpseo_focuskw',
            'wpseo_metakeywords',
            'wpseo_opengraph-title',
            'wpseo_opengraph-description',
            'wpseo_twitter-title',
            'wpseo_twitter-description',
            'wpseo_bctitle',
            
            // Rank Math SEO
            'rank_math_title',
            'rank_math_description',
            'rank_math_focus_keyword',
            'rank_math_facebook_title',
            'rank_math_facebook_description',
            'rank_math_twitter_title',
            'rank_math_twitter_description',
            
            // All in One SEO 
            'aioseo_title',
            'aioseo_description',
            'aioseo_og_title',
            'aioseo_og_description',
            'aioseo_twitter_title',
            'aioseo_twitter_description',
            'aioseo_keywords',
            'aioseo_focus_keyphrase',
            
            // SEOPress 
            '_seopress_titles_title',
            '_seopress_titles_desc',
            '_seopress_social_fb_title',
            '_seopress_social_fb_desc',
            '_seopress_social_twitter_title',
            '_seopress_social_twitter_desc',
            '_seopress_analysis_target_kw',
            
            // Slim SEO 
            'slim_seo_title',
            'slim_seo_description',
            'slim_seo_facebook_title',
            'slim_seo_facebook_description',
            'slim_seo_twitter_title',
            'slim_seo_twitter_description',
            'slim_seo_keywords',
            
            // Squirrly SEO
            'sq_title',
            'sq_description',
            'sq_keywords',
            'sq_og_title',
            'sq_og_description',
            'sq_tw_title',
            'sq_tw_description',
            
            // The SEO Framework
            'genesis_title',
            'genesis_description',
            'open_graph_title',
            'open_graph_description',
            'twitter_title',
            'twitter_description',
            'genesis_keywords',
            'tsf_title_no_blogname',
        ]);
    }

    public static function get_available_fields_for(int $id) : array {
        return apply_filters('pllat_available_term_meta_translation_fields_for', self::get_available_fields(), $id);
    }
}
