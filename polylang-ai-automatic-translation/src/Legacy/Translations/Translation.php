<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translations;

abstract class Translation implements Translation_Interface {
    
    protected $translation = [];

    public function get_all() {
        return $this->translation;
    }

    public function get_translation(string $key) {
        return !empty($this->translation[$key]) ? $this->translation[$key] : null;
    }

    public function set_field(string $key, $value) {
        $this->translation[$key] = $value;
    }
}