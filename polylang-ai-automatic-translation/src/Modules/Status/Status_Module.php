<?php
/**
 * Status_Module class file.
 *
 * @package Polylang AI Automatic Translation
 */

namespace PLLAT\Status;

use PLLAT\Status\Services\Debug_Data;
use PLLAT\Translator\Services\Bulk_Config;
use XWP\DI\Container;
use XWP\DI\Decorators\Module;

/**
 * Status module declaration.
 */
#[Module(
    hook: 'init',
    priority: 2,
    handlers: array(
        Controllers\Site_Health_Controller::class,
        Handlers\Site_Health_Handler::class,
    ),
)]
class Status_Module {
    /**
     * Get the module configuration.
     *
     * @return array<string,mixed>
     */
    public static function configure(): array {
        return array(
            'app.dbginfo'     => \DI\factory(
                static fn( Container $ctr ) => $ctr->call( Debug_Data::class ),
            ),
            'app.tests'       => \array_merge(
                ...\array_map(
                    static fn( $t ) => $t::define(),
                    array(
                        Services\License_Test::class,
                        Services\Scheduler_Test::class,
                    ),
                ),
            ),
            Debug_Data::class => \DI\create()->constructor(
                bc: \DI\get( Bulk_Config::class ),
                lc: \DI\get( \SC_License::class ),
            ),
        );
    }
}
