<?php //phpcs:disable Squiz.Commenting.FunctionComment.IncorrectTypeHint
/**
 * Site_Health_Controller class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Status
 */

namespace PLLAT\Status\Controllers;

use DI\Attribute\Inject;
use PLLAT\Status\Interfaces\Health_Test;
use WP_REST_Request;
use WP_REST_Response;
use WP_REST_Server;
use XWP\DI\Container;
use XWP\DI\Decorators\REST_Handler;
use XWP\DI\Decorators\REST_Route;

/**
 * Rest controller for health tests
 */
#[REST_Handler( namespace: 'pllat/v1', basename: 'health-tests' )]
class Site_Health_Controller extends \XWP_REST_Controller {
    /**
     * Constructor
     *
     * @param array<string,Health_Test> $tests Array of health tests.
     * @param Container                 $ctr The DI container.
     */
    public function __construct(
        #[Inject( 'app.tests' )] private readonly array $tests,
        private readonly Container $ctr,
    ) {
    }

    /**
     * Run the test callback.
     *
     * @param  WP_REST_Request<array<string,string>> $req Request object.
     * @return WP_REST_Response
     */
    #[REST_Route(
        route: '(?P<test>[\w-]+)',
        methods: WP_REST_Server::READABLE,
        vars: array(),
        guard: 'validate_request_permission',
    )]
    public function run_test( WP_REST_Request $req ): WP_REST_Response|\WP_Error {
        try {
            $test = $this->get_test( $req->get_param( 'test' ) );

            return \rest_ensure_response( $this->ctr->call( $test, $test->get_params() ) );
        } catch ( \InvalidArgumentException $e ) {
            return new \WP_Error(
                'test_not_found',
                \esc_html( $e->getMessage() ),
                array(
                    'status' => 404,
                ),
            );
        }
    }

    /**
     * Validates if the current user can request this REST endpoint.
     *
     * @param WP_REST_Request<array<string,string>> $req Request object.
     * @return bool
     */
    public function validate_request_permission( WP_REST_Request $req ) {
        $test  = 'site_health_test_rest_capability';
        $cap   = 'view_site_health_checks';
        $check = $req->get_param( 'test' );

        /**
         * Filters the capability needed to run a given Site Health check.
         *
         * @param  string $cap   The default capability required for this check.
         * @param  string $check The Site Health check being performed.
         * @return string
         */
        $capability = \apply_filters( "{$test}_{$check}", $cap, $check );

        return \current_user_can( $capability );
    }

    /**
     * Get a test by internal name
     *
     * @param  string $test The test name.
     * @return Health_Test
     *
     * @throws \InvalidArgumentException If the test is not found.
     */
    protected function get_test( string $test ): Health_Test {
        return $this->tests[ $test ] ?? throw new \InvalidArgumentException(
            \esc_html( "Test not found: {$test}" ),
            404,
        );
    }
}
