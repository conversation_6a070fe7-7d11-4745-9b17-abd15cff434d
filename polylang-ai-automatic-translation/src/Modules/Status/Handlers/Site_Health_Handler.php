<?php
/**
 * Site_Health_Handler class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Status
 */

namespace PLLAT\Status\Handlers;

use DI\Attribute\Inject;
use PLLAT\Status\Interfaces\Health_Test;
use XWP\DI\Container;
use XWP\DI\Decorators\Filter;
use XWP\DI\Decorators\Handler;

/**
 * <PERSON><PERSON> declaring the health tests for the plugin.
 */
#[Handler( tag: 'init', priority: 10, context: Handler::CTX_GLOBAL, strategy: Handler::INIT_JIT )]
class Site_Health_Handler {
    /**
     * Constructor
     *
     * @param array<string,Health_Test> $tests Array of health tests.
     * @param Container                 $ctr The DI container.
     */
    public function __construct(
        #[Inject( 'app.tests' )] private readonly array $tests,
        private readonly Container $ctr,
    ) {
    }


    /**
     * Add plugin tests to the site health tests.
     *
     * @param  array<string,array<string,mixed>> $tests The tests to add to.
     * @return array<string,array<string,mixed>>
     */
    #[Filter( tag: 'site_status_tests', priority: 100 )]
    public function add_health_tests( array $tests ): array {
        foreach ( $this->tests as $test ) {
            $tests[ $test->get_type()->value ][ $test->get_id() ] = $test->get_data( $this->ctr );
        }

        return $tests;
    }

    /**
     * Add debug data to the site health tests.
     *
     * @param  array<string,mixed> $debug_data The debug data to add to.
     * @param  array<string,mixed> $our_data   The data to add. Injected from the DI container.
     * @return array<string,mixed>
     */
    #[Filter( tag: 'debug_information', priority: 100, args: 1, params: array( 'app.dbginfo' ) )]
    public function add_debug_data( array $debug_data, array $our_data ): array {
        $debug_data['polylang-ai'] = $our_data;

        return $debug_data;
    }
}
