<?php //phpcs:disable

namespace PLLAT\Translator\Services;

use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Translatable_Post;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity;

class Bulk_Post_Manager extends Abstract_Bulk_Entity_Manager {

    public function __construct(
        Bulk_Config $config,
        Translator $translator,
        Language_Manager $language_manager,
        private readonly Post_Collector $query
    ) {
        parent::__construct($config, $translator, $language_manager);
    }

    public function get_translatable_ids(string $post_type, array $languages = []): array {
        return $this->query->get_translatable_ids(
            $post_type,
            $languages,
            $this->config->get_bulk_size()
        );
    }

    /**
     * Get all IDs for the given post type
     *
     * @param  string $post_type The post type to get IDs for.
     * @return array<int> An array of IDs for the given post type.
     */
    public function get_all_ids(string $post_type): array {
        return $this->query->get_all_ids($post_type);
    }

    public function prepare_item(int $id, array $languages = []): void {

        $post_manager = Helpers::get_translatable_post_manager($id);
        $translatable_post = $post_manager->get_translatable_entity();

        // Add all missing and unprocessed translations to the queue
        $post_manager->populate_queue($languages);

        // Make sure the queue only contains active languages
        $post_manager->tidy_queue();
    }

    public function process_item(int $id, string $language_slug, array $args = []): void {
        $post_manager = Helpers::get_translatable_post_manager($id);
        $post_manager->process_queue_item($language_slug, $args);
    }

    public function get_item(int $id): Translatable_Entity {
        return new Translatable_Post($id, $this->language_manager);
    }
}
