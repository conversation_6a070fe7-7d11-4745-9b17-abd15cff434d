<?php //phpcs:disable

namespace PLLAT\Translator\Services;

use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Strings_Manager;

class Bulk_Service {

    private Strings_Manager $strings_manager;

    public function __construct(
        private readonly Bulk_Manager $bulk_manager,
        private readonly Bulk_Stats_Manager $stats_manager,
        private readonly Bulk_Config $config,
        private readonly Bulk_Logger $bulk_logger,
        private readonly Language_Manager $language_manager,
    ) {
        $this->strings_manager = new Strings_Manager($this->language_manager);
    }

    public function start(): void {
        $this->bulk_logger->start_new_log();
        $this->config->activate();
    }

    public function stop(): void {
        $this->config->deactivate();
        $this->config->set_running(false);
    }

    public function is_running(): bool {
        return $this->config->is_running();
    }

    public function set_running(bool $running): void {
        $this->config->set_running($running);
    }

    public function is_stopped(): bool {
        return $this->config->is_stopped();
    }

    public function is_activated(): bool {
        return $this->config->is_activated();
    }

    public function get_status(): string {
        $is_stopped = $this->is_stopped();
        $is_running = $this->is_running();
        $is_activated = $this->is_activated();

        if ($is_stopped) {
            return 'stopped';
        }
        if ($is_activated && $is_running) {
            return 'running';
        }
        return 'activated';
    }

    /**
     * Get the active post types.
     *
     * @return array<string>
     */
    public function get_active_post_types(): array {
        return $this->config->get_active_post_types();
    }

    /**
     * Set the active post types.
     *
     * @param array<string> $post_types Array of post type slugs.
     */
    public function set_active_post_types(array $post_types): void {
        $this->config->set_active_post_types($post_types);
    }

    /**
     * Get the active taxonomies.
     *
     * @return array<string>
     */
    public function get_active_taxonomies(): array {
        return $this->config->get_active_taxonomies();
    }

    /**
     * Set the active taxonomies.
     *
     * @param array<string> $taxonomies Array of taxonomy slugs.
     */
    public function set_active_taxonomies(array $taxonomies): void {
        $this->config->set_active_taxonomies($taxonomies);
    }

    /**
     * Get the default language.
     *
     * @return string
     */
    public function get_default_language(): string {
        return $this->config->get_default_language();
    }

    /**
     * Get the active languages.
     *
     * @return array<string>
     */
    public function get_active_languages(): array {
        return array_values($this->config->get_active_languages());
    }

    /**
     * Set the active languages.
     *
     * @param array<string> $languages Array of language slugs.
     */
    public function set_active_languages(array $languages): void {
        $this->config->set_active_languages($languages);
    }

    public function get_active_string_groups(): array {
        return $this->config->get_active_string_groups();
    }

    public function set_active_string_groups(array $string_groups): void {
        $this->config->set_active_string_groups($string_groups);
    }

    public function get_term_filters(): array {
        return $this->bulk_manager->get_all_term_filters();
    }

    public function set_term_filters(array $term_filters): void {
        $this->bulk_manager->set_all_term_filters($term_filters);
    }

    public function get_term_filters_for_post_type(string $post_type): array {
        return $this->bulk_manager->get_term_filters_for_post_type($post_type);
    }

    public function set_term_filters_for_post_type(string $post_type, array $filters): void {
        $this->bulk_manager->set_term_filters_for_post_type($post_type, $filters);
    }

    public function get_taxonomies_with_terms(string $post_type): array {
        return $this->bulk_manager->get_taxonomies_with_terms($post_type);
    }

    public function prepare(): void {
        $languages  = $this->config->get_active_languages();
        $post_types = $this->config->get_active_post_types();
        $taxonomies = $this->config->get_active_taxonomies();
        $string_groups = $this->config->get_active_string_groups();

        // Build queue
        $this->bulk_manager->build_queue($post_types, $taxonomies, $string_groups, $languages);
        $queue = $this->bulk_manager->get_queue();

        $this->bulk_logger->reset_current_log_file();
        $this->stats_manager->delete_stats();

        if (!empty($taxonomies)) {
            foreach ($taxonomies as $taxonomy) {
                $this->stats_manager->calculate_stats($queue, 'terms', $taxonomy, $languages);
            }
        }

        if (!empty($post_types)) {
            foreach ($post_types as $post_type) {
                $this->stats_manager->calculate_stats($queue, 'posts', $post_type, $languages);
            }
        }

        if (!empty($string_groups)) {
            $string_start = microtime(true);
            foreach ($string_groups as $string_group) {
                $this->stats_manager->calculate_stats($queue, 'strings', $string_group, $languages);
            }
        }
        $this->stats_manager->store_stats();
    }

    public function process(): void {
        $post_types = $this->config->get_active_post_types();
        $taxonomies = $this->config->get_active_taxonomies();
        $string_groups = $this->config->get_active_string_groups();
        $languages  = $this->config->get_active_languages();
        $queue = $this->bulk_manager->get_queue();

        $this->bulk_manager->schedule_strings($queue, $string_groups, $languages);
        $this->bulk_manager->schedule_posts($queue, $post_types, $languages);
        $this->bulk_manager->schedule_terms($queue, $taxonomies, $languages);
    }

    public function get_stats(): Bulk_Stats {
        return $this->stats_manager->get_stats();
    }

    public function delete_stats(): void {
        $this->stats_manager->delete_stats();
    }

    public function get_bulk_size(): int {
        return $this->config->get_bulk_size();
    }

    public function set_bulk_size(int $size): void {
        $this->config->set_bulk_size($size);
    }

    public function get_additional_instructions(): string {
        return $this->config->get_additional_instructions();
    }

    public function set_additional_instructions(string $instructions): void {
        $this->config->set_additional_instructions($instructions);
    }

    public function get_force_mode(): bool {
        return $this->config->get_force_mode();
    }

    public function set_force_mode(bool $force_mode): void {
        $this->config->set_force_mode($force_mode);
    }

    /**
     * Get the available languages.
     *
     * @param  bool $exclude_default Whether to exclude the default language.
     * @return array<array{slug: string, label: string, flag: string}>
     */
    public function get_available_languages(bool $exclude_default = false): array {
        $languages = $this->language_manager->get_available_languages($exclude_default);
        if (empty($languages)) {
            return [];
        }
        return array_values(array_map(function($language) {
            $language_data = $this->language_manager->get_language_data($language);
            return [
                'slug' => $language_data['slug'],
                'label' => $language_data['name'],
                'flag' => $language_data['flag'],
            ];
        }, $languages));
    }

    /**
     * Get the available post types.
     *
     * @return array<array{slug: string, label: string}>
     */
    public function get_available_post_types(): array {
        $post_types = Helpers::get_available_post_types();

        return array_values(array_map(function($post_type) {
            $post_type_object = get_post_type_object($post_type);
            return [
                'slug' => $post_type,
                'label' => ucfirst($post_type_object->labels->singular_name)
            ];
        }, $post_types));
    }

    /**
     * Get the available taxonomies.
     *
     * @return array<array{slug: string, label: string}>
     */
    public function get_available_taxonomies(): array {
        $taxonomies = Helpers::get_available_taxonomies();
        if (empty($taxonomies)) {
            return [];
        }
        return array_values(array_map(function($taxonomy) {
            $taxonomy_object = get_taxonomy($taxonomy);
            return [
                'slug' => $taxonomy,
                'label' => ucfirst($taxonomy_object->labels->singular_name)
            ];
        }, $taxonomies));
    }

    /**
     * Get the available string groups.
     *
     * @return array<array{slug: string, label: string}>
     */
    public function get_available_string_groups(): array {
        $string_groups = $this->strings_manager->get_string_groups();
        if (empty($string_groups)) {
            return [];
        }
        return array_values(array_map(function($string_group) {
            return [
                'slug' => $string_group,
                'label' => ucfirst($string_group),
            ];
        }, $string_groups));
    }

    /**
     * Get the logs.
     *
     * @return array<string>
     */
    public function get_logs(): array {
        return $this->bulk_logger->get_logs();
    }

    public function can_log(): bool {
        return $this->bulk_logger->can_log();
    }
}
