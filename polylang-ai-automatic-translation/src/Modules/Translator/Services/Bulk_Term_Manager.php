<?php //phpcs:disable

namespace PLLAT\Translator\Services;

use EPIC_WP\Polylang_Automatic_AI_Translation\Bulk\Query\Term_Query;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Terms\Translatable_Term;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator;
use PLLAT\Translator\Services\Bulk_Config;

class Bulk_Term_Manager extends Abstract_Bulk_Entity_Manager {
    private Term_Query $query;

    public function __construct( Bulk_Config $config, Translator $translator, Language_Manager $language_manager ) {
        parent::__construct( $config, $translator, $language_manager );
        $this->query = new Term_Query( $config, array( 'publish', 'future', 'draft', 'pending', 'private' ) );
    }

    public function get_translatable_ids( string $taxonomy, array $languages = array() ): array {
        return $this->query->get_translatable_ids(
            $taxonomy,
            $languages,
            $this->config->get_bulk_size(),
        );
    }

    /**
     * Get all IDs of the terms in the given taxonomy.
     *
     * @param  string $taxonomy The taxonomy to get the IDs from.
     * @return array<int>
     */
    public function get_all_ids( string $taxonomy ): array {
        return $this->query->get_all_ids( $taxonomy );
    }

    public function prepare_item( int $id, array $languages = array() ): void {
        $term_manager      = Helpers::get_translatable_term_manager( $id );
        $translatable_term = $term_manager->get_translatable_entity();

        // Add all missing and unprocessed translations to the queue
        $term_manager->populate_queue();

        // Make sure the queue only contains active languages
        $term_manager->tidy_queue();
    }

    public function process_item( int $id, string $language_slug, array $args = array() ): void {
        $term_manager = Helpers::get_translatable_term_manager( $id );
        $term_manager->process_queue_item( $language_slug, $args );
    }

    public function get_item( int $id ): Translatable_Entity {
        return new Translatable_Term( $id, $this->language_manager );
    }
}
