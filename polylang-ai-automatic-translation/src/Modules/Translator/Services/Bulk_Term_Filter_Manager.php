<?php //phpcs:disable

namespace PLLAT\Translator\Services;

use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;

class Bulk_Term_Filter_Manager {

    public function __construct( private readonly Bulk_Config $config ) {
    }

    /**
     * Get all available terms for a taxonomy in the default language
     *
     * @param  string $taxonomy The taxonomy to get the terms from.
     * @return array<array{
     *   id: int,
     *   slug: string,
     *   name: string,
     *   count: int
     * }>
     */
    public function get_available_terms(string $taxonomy): array {
        $default_language = \pll_default_language();

        $terms = \get_terms([
            'taxonomy' => $taxonomy,
            'hide_empty' => true,
            'lang' => $default_language,
        ]);

        if (\is_wp_error($terms) || empty($terms)) {
            return [];
        }

        return array_values(array_map(function($term) {
            return [
                'id' => $term->term_id,
                'slug' => $term->slug,
                'name' => $term->name,
                'count' => $term->count,
            ];
        }, $terms));
    }

    /**
     * Get all available taxonomies for a post type with their terms
     *
     * @param string $post_type
     * @return array<array{
     *   slug: string,
     *   label: string,
     *   terms: array<array{
     *     id: int,
     *     slug: string,
     *     name: string,
     *     count: int
     *   }>
     * }>
     */
    public function get_taxonomies_with_terms(string $post_type): array {

        $active_taxonomies = Helpers::get_available_taxonomies();
        $taxonomies = \get_object_taxonomies($post_type, 'objects');

        if (empty($taxonomies)) {
            return [];
        }

        $result = [];

        foreach ($taxonomies as $taxonomy) {
            // Only include taxonomies that Polylang can translate and are activated
            if (in_array($taxonomy->name, $active_taxonomies)) {
                $result[] = [
                    'slug' => $taxonomy->name,
                    'label' => $taxonomy->label,
                    'terms' => $this->get_available_terms($taxonomy->name),
                ];
            }
        }

        return $result;
    }

    /**
     * Get the term filters for a post type
     *
     * @param  string $post_type Post type slug.
     * @return array<mixed>
     */
    public function get_term_filters_for_post_type(string $post_type): array {
        $term_filters = $this->config->get_term_filters();
        return isset($term_filters[$post_type]) ? $term_filters[$post_type] : [];
    }

    /**
     * Get all term filters
     *
     * @return array<string,mixed>
     */
    public function get_all_term_filters(): array {
        return $this->config->get_term_filters();
    }

    /**
     * Set term filters for a post type
     *
     * @param string       $post_type Post type slug.
     * @param array<mixed> $filters   Array of term filters for the post type.
     */
    public function set_term_filters_for_post_type(string $post_type, array $filters): void {
        $term_filters = $this->config->get_term_filters();
        $term_filters[$post_type] = $filters;
        $this->config->set_term_filters($term_filters);
    }

    /**
     * Set all term filters
     *
     * @param array<mixed> $filters Array of term filters for all post types.
     */
    public function set_all_term_filters(array $filters): void {
        $this->config->set_term_filters($filters);
    }

    /**
     * Check if a post type has any active term filters
     *
     * @param string $post_type
     * @return bool
     */
    public function has_active_term_filters(string $post_type): bool {
        $term_filters = $this->get_term_filters_for_post_type($post_type);

        if (empty($term_filters)) {
            return false;
        }

        // Check if any taxonomy has terms selected
        foreach ($term_filters as $taxonomy => $term_ids) {
            if (!empty($term_ids)) {
                return true;
            }
        }

        return false;
    }
}
