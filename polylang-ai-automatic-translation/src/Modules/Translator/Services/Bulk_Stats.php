<?php //phpcs:disable

namespace PLLAT\Translator\Services;

class Bulk_Stats {

    /**
     * Default stats structure
     *
     * @var array{
     *   posts: array<mixed>,
     *   terms: array<mixed>,
     *   strings: array<mixed>,
     *   total: int,
     *   processed: int,
     * }
     */
    private array $stats = [
        'posts' => [],
        'terms' => [],
        'strings' => [],
        'total' => 0,
        'processed' => 0,
    ];

    /**
     * Undocumented function
     *
     * @param  array{
     *   posts?: array<mixed>,
     *   terms?: array<mixed>,
     *   strings?: array<mixed>,
     *   total?: int,
     *   processed?: int,
     * } $stats Initial stats.
     */
    public function __construct(array $stats = []) {
        $this->stats = array_merge($this->stats, $stats);
    }

    /**
     * Reset the stats to their initial state
     */
    public function reset(): void{
        $this->stats = [
            'posts' => [],
            'terms' => [],
            'strings' => [],
            'total' => 0,
            'processed' => 0,
        ];
    }

    public function update_stats_total(string $type, string $entity, string $language, int $count): void {
        $this->stats[$type][$entity][$language]['total'] = ($this->stats[$type][$entity][$language]['total'] ?? 0) + $count;
        if (!isset($this->stats[$type][$entity][$language]['processed'])) {
            $this->stats[$type][$entity][$language]['processed'] = 0;
        }
        $this->calculate_stats_total();
    }

    public function update_stats_processed(string $type, string $entity, string $language, int $count): void {
        $this->stats[$type][$entity][$language]['processed'] = max(0, ($this->stats[$type][$entity][$language]['processed'] ?? 0) + $count);
        $this->calculate_stats_processed();
    }

    /**
     * Get the stats
     *
     * @return array{
     *   posts?: array<mixed>,
     *   terms?: array<mixed>,
     *   strings?: array<mixed>,
     *   total?: int,
     *   processed?: int,
     * }
     */
    public function get_stats(): array {
        return $this->stats;
    }

    /**
     * Get the stats for posts.
     *
     * @return array<mixed>
     */
    public function get_stats_posts(): array {
        return $this->stats['posts'];
    }

    /**
     * Get the stats for terms.
     *
     * @return array<mixed>
     */
    public function get_stats_terms() {
        return $this->stats['terms'];
    }

    /**
     * Get the stats for strings.
     *
     * @return array<mixed>
     */
    public function get_stats_strings(): array {
        return $this->stats['strings'];
    }

    /**
     * Get the total number of items in the queue.
     *
     * @return int
     */
    public function get_stats_total(): int {
        return $this->stats['total'];
    }

    public function get_stats_processed(): int {
        return $this->stats['processed'];
    }

    public function is_queue_empty(): bool {
        return ($this->stats['total'] === $this->stats['processed']);
    }

    private function calculate_stats_total(): void {
        $this->stats['total'] =
            $this->sum_recursive($this->stats['posts'], 'total') +
            $this->sum_recursive($this->stats['terms'], 'total') +
            $this->sum_recursive($this->stats['strings'], 'total');
    }

    private function calculate_stats_processed(): void {
        $this->stats['processed'] =
            $this->sum_recursive($this->stats['posts'], 'processed') +
            $this->sum_recursive($this->stats['terms'], 'processed') +
            $this->sum_recursive($this->stats['strings'], 'processed');
    }

    /**
     * Recursively sum the values of a specific type in a nested array.
     *
     * @param  array<mixed> $array The array to search.
     * @param  string       $type  The key to sum.
     * @return int
     */
    private function sum_recursive(array $array, string $type): int {
        $sum = 0;
        foreach ($array as $key => $value) {
            if ($key === $type && is_numeric($value)) {
                $sum += (int)$value;
            } elseif (is_array($value)) {
                $sum += $this->sum_recursive($value, $type);
            }
        }
        return $sum;
    }
}
