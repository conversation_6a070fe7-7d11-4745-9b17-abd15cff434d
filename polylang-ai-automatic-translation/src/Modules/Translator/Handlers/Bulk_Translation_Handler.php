<?php //phpcs:disable

namespace PLLAT\Translator\Handlers;

use WP_REST_Response;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Action_Scheduler\Action_Scheduler_Health_Check;
use PLLAT\Translator\Services\Bulk_Action_Scheduler;
use PLLAT\Translator\Services\Bulk_Logger;
use PLLAT\Translator\Services\Bulk_Service;
use WP_Error;
use WP_REST_Request;
use XWP\DI\Decorators\Action;
use XWP\DI\Decorators\Filter;
use XWP\DI\Decorators\Handler;

/**
 * Handles bulk translation stuff.
 *
 * @phpstan-type RestReq array<string,mixed>
 */
#[Handler(tag: 'init', priority: 0)]
class Bulk_Translation_Handler {

    public function __construct(
        private readonly Bulk_Service $bulk_service,
        private readonly Bulk_Logger $bulk_logger,
        private readonly Bulk_Action_Scheduler $bulk_scheduler,
    ) {
    }

    /**
     * Makes sure that products are always processed before product variations
     *
     * @param  array<string,mixed> $post_types Array of post types.
     * @return array<string,mixed>             Array of post types with products first.
     */
    #[Filter(tag: 'pllat_get_active_bulk_translation_post_types', priority: 10)]
    public function order_active_bulk_post_types(array $post_types): array {
        if (is_array($post_types) && in_array('product', $post_types)) {
            $product_index = array_search('product', $post_types);
            unset($post_types[$product_index]);
            array_unshift($post_types, 'product');
        }
        return $post_types;
    }

    #[Action(tag: 'rest_api_init', priority: 10, context: Action::CTX_REST)]
    public function register_rest_routes(): void {

        // Get data
        register_rest_route('pllat/v1', 'bulk-translator', [
            'methods' => 'GET',
            'callback' => [$this, 'handle_rest_get_ui_data'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Start bulk translation
        register_rest_route('pllat/v1', 'bulk-translator/start', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_start'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Stop bulk translation
        register_rest_route('pllat/v1', 'bulk-translator/stop', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_stop'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Save language settings
        register_rest_route('pllat/v1', 'bulk-translator/settings/languages', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_save_languages'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Save post type settings
        register_rest_route('pllat/v1', 'bulk-translator/settings/post-types', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_save_post_types'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Save taxonomies settings
        register_rest_route('pllat/v1', 'bulk-translator/settings/taxonomies', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_save_taxonomies'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Save string groups settings
        register_rest_route('pllat/v1', 'bulk-translator/settings/string-groups', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_save_string_groups'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Save bulk size
        register_rest_route('pllat/v1', 'bulk-translator/settings/bulk-size', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_save_bulk_size'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Save additional instructions
        register_rest_route('pllat/v1', 'bulk-translator/settings/additional-instructions', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_save_additional_instructions'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Get latest stats
        register_rest_route('pllat/v1', 'bulk-translator/stats', [
            'methods' => 'GET',
            'callback' => [$this, 'handle_rest_get_latest_stats'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Prepare bulk translation
        register_rest_route('pllat/v1', 'bulk-translator/prepare', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_prepare'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Get logs
        register_rest_route('pllat/v1', 'bulk-translator/logs', [
            'methods' => 'GET',
            'callback' => [$this, 'handle_rest_get_logs'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Get realtime updates
        register_rest_route('pllat/v1', 'bulk-translator/realtime', [
            'methods' => 'GET',
            'callback' => [$this, 'handle_rest_get_realtime_updates'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Save Action Scheduler settings
        register_rest_route('pllat/v1', 'bulk-translator/settings/action-scheduler', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_save_action_scheduler_settings'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Get Action Scheduler settings
        register_rest_route('pllat/v1', 'bulk-translator/settings/action-scheduler', [
            'methods' => 'GET',
            'callback' => [$this, 'handle_rest_get_action_scheduler_settings'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Save force mode
        register_rest_route('pllat/v1', 'bulk-translator/settings/force-mode', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_save_force_mode'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Get taxonomy terms for post types
        register_rest_route('pllat/v1', 'bulk-translator/taxonomy-terms', [
            'methods' => 'GET',
            'callback' => [$this, 'handle_rest_get_taxonomy_terms'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);

        // Save term filters for post types
        register_rest_route('pllat/v1', 'bulk-translator/settings/term-filters', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_rest_save_term_filters'],
            'permission_callback' => [$this, 'permissions_check'],
        ]);
    }


    public function permissions_check(): bool {
        return current_user_can('manage_options');
    }

    public function handle_rest_get_ui_data(): WP_REST_Response|WP_Error {

        $action_scheduler_health_check = new Action_Scheduler_Health_Check();
        $health_check = $action_scheduler_health_check->check_action_scheduler_health();

        $scheduler_config = $this->bulk_scheduler->get_config();

        $response_data = [
            'status' => $this->bulk_service->get_status(),
            'languages' => $this->bulk_service->get_available_languages(true),
            'postTypes' => $this->bulk_service->get_available_post_types(),
            'taxonomies' => $this->bulk_service->get_available_taxonomies(),
            'stringGroups' => $this->bulk_service->get_available_string_groups(),
            'settings' => [
                'languages' => $this->bulk_service->get_active_languages(),
                'postTypes' => $this->bulk_service->get_active_post_types(),
                'taxonomies' => $this->bulk_service->get_active_taxonomies(),
                'stringGroups' => $this->bulk_service->get_active_string_groups(),
                'termFilters' => $this->bulk_service->get_term_filters(),
                'bulkSize' => $this->bulk_service->get_bulk_size(),
                'additionalInstructions' => $this->bulk_service->get_additional_instructions(),
                'forceMode' => $this->bulk_service->get_force_mode(),
                'actionScheduler' => [
                    'time_limit' => $scheduler_config->get_time_limit(),
                    'batch_size' => $scheduler_config->get_batch_size(),
                ],
            ],
            'strings' => [
                'settings' => [
                    'bulkSize' => __('Bulk Size'),
                    'additionalInstructions' => __('Additional Instructions'),
                    'save' => __('Save'),
                    'forceMode' => __('Enable Force Mode'),
                    'forceModeHelp' => __('Force mode will generate translations again for all fields, regardless of already existing translations for the selected languages.'),
                ],
            ],
            'stats' => $this->bulk_service->get_stats()->get_stats(),
            'logs' => $this->bulk_service->get_logs(),
            'actionSchedulerHealthCheck' => $health_check,
        ];

        return new WP_REST_Response($response_data);
    }

    /**
     * Handles the REST API request to save the selected languages for bulk translation.)
     *
     * @param  WP_REST_Request<RestReq>           $request REST API request object.
     * @return WP_REST_Response|WP_Error
     */
    public function handle_rest_save_languages(WP_REST_Request $request):WP_REST_Response|WP_Error {
        if ($this->bulk_service->is_running() || $this->bulk_service->is_activated()) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Bulk translation is running/activated. Please stop it before changing the languages.',
            ], 400);
        }

        $languages = $request->get_param('languages');
        $this->bulk_service->set_active_languages($languages);
        $this->bulk_service->prepare();
        return new WP_REST_Response(['status' => 'success']);
    }

    /**
     * Handles the REST API request to save the selected post types for bulk translation.
     *
     * @param  WP_REST_Request<RestReq>           $request REST API request object.
     * @return WP_REST_Response|WP_Error
     */
    public function handle_rest_save_post_types(WP_Rest_Request $request):WP_REST_Response|WP_Error {
        if ($this->bulk_service->is_running() || $this->bulk_service->is_activated()) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Bulk translation is running/activated. Please stop it before changing the post types.',
            ], 400);
        }

        $post_types = $request->get_param('postTypes');
        $this->bulk_service->set_active_post_types($post_types);
        $this->bulk_service->prepare();
        return new WP_REST_Response(['status' => 'success']);
    }

    /**
     * Handles the REST API request to save the selected taxonomies for bulk translation.
     *
     * @param  WP_REST_Request<RestReq>           $request REST API request object.
     * @return WP_REST_Response|WP_Error
     */
    public function handle_rest_save_taxonomies(WP_Rest_Request $request):WP_REST_Response|WP_Error {

        if ($this->bulk_service->is_running() || $this->bulk_service->is_activated()) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Bulk translation is running/activated. Please stop it before changing the taxonomies.',
            ], 400);
        }

        $taxonomies = $request->get_param('taxonomies');
        $this->bulk_service->set_active_taxonomies($taxonomies);
        $this->bulk_service->prepare();
        return new WP_REST_Response(['status' => 'success']);
    }

    /**
     * Handles the REST API request to save the selected string groups for bulk translation.
     *
     * @param  WP_REST_Request<RestReq>           $request REST API request object.
     * @return WP_REST_Response|WP_Error
     */
    public function handle_rest_save_string_groups(WP_Rest_Request $request):WP_REST_Response|WP_Error {

        if ($this->bulk_service->is_running() || $this->bulk_service->is_activated()) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Bulk translation is running/activated. Please stop it before changing the string groups.',
            ], 400);
        }

        $string_groups = $request->get_param('stringGroups');
        $this->bulk_service->set_active_string_groups($string_groups);
        $this->bulk_service->prepare();
        return new WP_REST_Response(['status' => 'success']);
    }

    /**
     * Handles the REST API request to save the bulk size for bulk translation.
     *
     * @param  WP_REST_Request<RestReq>           $request REST API request object.
     * @return WP_REST_Response|WP_Error
     */
    public function handle_rest_save_bulk_size(WP_Rest_Request $request):WP_REST_Response|WP_Error {

        if ($this->bulk_service->is_running() || $this->bulk_service->is_activated()) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Bulk translation is running/activated. Please stop it before changing the bulk size.',
            ], 400);
        }

        $bulk_size = $request->get_param('bulkSize');
        $bulk_size = $bulk_size > 3000 ? 3000 : $bulk_size;
        $this->bulk_service->set_bulk_size($bulk_size);
        $this->bulk_service->prepare();
        return new WP_REST_Response(['status' => 'success']);
    }

    /**
     * Handles the REST API request to save the additional instructions for bulk translation.
     *
     * @param  WP_REST_Request<RestReq>           $request REST API request object.
     * @return WP_REST_Response|WP_Error
     */
    public function handle_rest_save_additional_instructions(WP_Rest_Request $request):WP_REST_Response|WP_Error {

        if ($this->bulk_service->is_running() || $this->bulk_service->is_activated()) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Bulk translation is running/activated. Please stop it before changing the additional instructions.',
            ], 400);
        }

        $additional_instructions = $request->get_param('additionalInstructions');
        $this->bulk_service->set_additional_instructions($additional_instructions);
        $this->bulk_service->prepare();
        return new WP_REST_Response(['status' => 'success']);
    }

    /**
     * Handles the REST API request to save the force mode for bulk translation.
     *
     * @param  WP_REST_Request<RestReq>           $request REST API request object.
     * @return WP_REST_Response|WP_Error
     */
    public function handle_rest_save_force_mode(WP_Rest_Request $request):WP_REST_Response|WP_Error {
        $force_mode = $request->get_param('forceMode');
        $this->bulk_service->set_force_mode($force_mode);
        $this->bulk_service->prepare();
        return new WP_REST_Response(['status' => 'success']);
    }

    public function handle_rest_start():WP_REST_Response|WP_Error {
        if ($this->bulk_service->is_running() || $this->bulk_service->is_activated()) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Bulk translation is running/activated. Please stop it before starting a new bulk translation.',
            ], 400);
        }

        try {
            $this->bulk_scheduler->clear_scheduled_actions();
            $this->bulk_scheduler->clear_async_errors();
            $this->bulk_service->prepare();
            $this->bulk_service->start();
            $stats = $this->bulk_service->get_stats();
            if ($stats->is_queue_empty()) {
                $this->stop();
                Helpers::log('Bulk translation stopped.');
            } else {
                $this->run();
            }
            return new WP_REST_Response(['status' => 'success']);
        } catch (\Throwable $e) {
            Helpers::log($e->getMessage() . ' - ' . $e->getTraceAsString(), 'error');
            return new WP_REST_Response([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function handle_rest_stop() :WP_REST_Response|WP_Error{
        try {
            $this->stop();
            $this->bulk_scheduler->clear_async_errors();
            Helpers::log('Bulk translation stopped.');
            return new WP_REST_Response([
                'status' => 'success',
            ]);
        } catch (\Throwable $e) {
            Helpers::log($e->getMessage() . ' - ' . $e->getTraceAsString(), 'error');
            return new WP_REST_Response([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function handle_rest_get_latest_stats() :WP_REST_Response|WP_Error{
        $stats = $this->bulk_service->get_stats()->get_stats();
        return new WP_REST_Response($stats);
    }

    public function handle_rest_get_logs() :WP_REST_Response|WP_Error{
        $logs = $this->bulk_logger->get_logs();
        return new WP_REST_Response($logs);
    }

    public function handle_rest_prepare():WP_REST_Response|WP_Error {
        if (!$this->bulk_service->is_running()) {
            $this->bulk_service->prepare();
            $this->bulk_scheduler->clear_async_errors();
        }
        return new WP_REST_Response([
            'status' => 'success',
        ]);
    }

    public function handle_rest_get_realtime_updates():WP_REST_Response|WP_Error {
        $stats = $this->bulk_service->get_stats()->get_stats();
        $logs = $this->bulk_logger->get_logs();
        $status = $this->bulk_service->get_status();
        $errors = $this->bulk_scheduler->get_async_errors(3);

        return new WP_REST_Response([
            'status' => $status,
            'stats' => $stats,
            'logs' => $logs,
            'errors' => $errors,
        ]);
    }

    public function run():void {
        try {
            Helpers::log('Try to run bulk translation.');

            // If the bulk translation is not activated, stop it
            if (!$this->bulk_service->is_activated()) {
                Helpers::log('Bulk translation is not activated. Skipping..', 'warning');
                return;
            }

            // If the bulk translation is already running, don't run it again
            if ($this->bulk_service->is_running()) {
                Helpers::log('Bulk translation is already running. Skipping..', 'warning');
                return;
            } else {
                Helpers::log('Bulk translation is not running yet. Starting..');
            }

            //$this->bulk_service->set_running(true);
            Helpers::log('Bulk starts processing.');
            $this->bulk_service->process();
            $stats = $this->bulk_service->get_stats();
            if ($stats->is_queue_empty()) {
                $this->stop();
                Helpers::log('Bulk translation stopped.');
            }
        } catch (\Throwable $e) {
            Helpers::log($e->getMessage() . ' - ' . $e->getTraceAsString(), 'error');
            $this->log_async_error($e->getMessage());
        }
    }

    public function stop(): WP_REST_Response|WP_Error {
        $this->bulk_service->stop();
        $this->bulk_scheduler->clear_scheduled_actions();
        return new WP_REST_Response([
            'status' => 'success',
        ]);
    }



    private function log_async_error(string $message): bool {
        return set_transient('pllat_bulk_async_error', $message, 60);
    }

    public function handle_rest_get_action_scheduler_settings():WP_REST_Response|WP_Error {
        $scheduler_config = $this->bulk_scheduler->get_config();

        return new WP_REST_Response([
            'time_limit' => $scheduler_config->get_time_limit(),
            'batch_size' => $scheduler_config->get_batch_size()
        ]);
    }

    /**
     * Handles the REST API request to save the Action Scheduler settings.
     *
     * @param  WP_REST_Request<RestReq>           $request REST API request object.
     * @return WP_REST_Response|WP_Error
     */
    public function handle_rest_save_action_scheduler_settings(WP_REST_Request $request): WP_REST_Response|WP_Error{
        if ($this->bulk_service->is_running() || $this->bulk_service->is_activated()) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Bulk translation is running/activated. Please stop it before changing the Action Scheduler settings.',
            ], 400);
        }

        $scheduler_config = $this->bulk_scheduler->get_config();

        $time_limit = $request->get_param('time_limit');
        $batch_size = $request->get_param('batch_size');

        if ($time_limit !== null) {
            $scheduler_config->set_time_limit((int)$time_limit);
        }
        if ($batch_size !== null) {
            $scheduler_config->set_batch_size((int)$batch_size);
        }

        return new WP_REST_Response(['status' => 'success']);
    }

    /**
     * Handles the REST API request to get the taxonomy terms for a given post type.
     *
     * @param  WP_REST_Request<RestReq>           $request REST API request object.
     * @return WP_REST_Response|WP_Error
     */
    public function handle_rest_get_taxonomy_terms(WP_REST_Request $request): WP_REST_Response|WP_Error {
        $post_type = $request->get_param('post_type');

        if (empty($post_type)) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Post type is required',
            ], 400);
        }

        $terms = $this->bulk_service->get_taxonomies_with_terms($post_type);

        return new WP_REST_Response($terms);
    }

    /**
     * Handles the REST API request to save the selected term filters for bulk translation.
     *
     * @param  WP_REST_Request<RestReq>           $request REST API request object.
     * @return WP_REST_Response|WP_Error
     */
    public function handle_rest_save_term_filters(WP_REST_Request $request): WP_REST_Response|WP_Error {
        if ($this->bulk_service->is_running() || $this->bulk_service->is_activated()) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Bulk translation is running/activated. Please stop it before changing the term filters.',
            ], 400);
        }

        $filters = $request->get_param('termFilters');

        if (!is_array($filters)) {
            return new WP_REST_Response([
                'status' => 'error',
                'message' => 'Term filters must be an array',
            ], 400);
        }

        $this->bulk_service->set_term_filters($filters);
        $this->bulk_service->prepare();

        return new WP_REST_Response(['status' => 'success']);
    }
}
