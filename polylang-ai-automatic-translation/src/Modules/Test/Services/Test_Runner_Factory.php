<?php

namespace PLLAT\Test\Services;

use EPIC_WP\Polylang_Automatic_AI_Translation\Content_String_Editor\HTML_Content_String_Editor;
use EPIC_WP\Polylang_Automatic_AI_Translation\Content_String_Editor\WP_Block_Content_String_Editor;
use EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\Elementor\Elementor_Content_Editor;
use PLLAT\Common\Interfaces\Extracts_String;

class Test_Runner_Factory {
    /**
     * Associative array of extractors.
     *
     * @var array<string,class-string<Extracts_String>>
     */
    protected array $ext_classes = array(
        'block'     => WP_Block_Content_String_Editor::class,
        'content'   => HTML_Content_String_Editor::class,
        'elementor' => Elementor_Content_Editor::class,
    );

    /**
     * Get the test files.
     *
     * @param  string $basedir The base directory.
     * @return array<string,string>
     */
    public static function get_test_files( string $basedir ): array {
        $basedir .= 'testing/content';
        if ( ! \is_dir( $basedir ) ) {
            return array();
        }

        $files = \array_map(
            static fn( $f ) => $basedir . '/' . $f['name'],
            \xwp_wpfs()->dirlist( $basedir ),
        );

        \ksort( $files );

        return $files;
    }

    /**
     * Get the extractors.
     *
     * @return array<string>
     */
    public function get_extractors(): array {
        return \array_keys( $this->ext_classes );
    }

    public function make_extractor( string $type ): Extracts_String {
        return new $this->ext_classes[ $type ]();
    }
}
