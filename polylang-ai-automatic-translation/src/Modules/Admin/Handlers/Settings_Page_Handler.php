<?php
/**
 * Settings_Page_Handler class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Admin
 */

namespace PLLAT\Admin\Handlers;

use EPIC_WP\Polylang_Automatic_AI_Translation\Debug_Log;
use PLLAT\Admin\Services\Settings_Form;
use SC_License;
use XWP\DI\Decorators\Action;
use XWP\DI\Decorators\Filter;
use XWP\DI\Decorators\Handler;

/**
 * Handles settings page display and settings fields registration
 */
#[Handler( tag: 'init', priority: 11, context: Handler::CTX_ADMIN )]
class Settings_Page_Handler {
    /**
     * Constructor
     *
     * @param Settings_Form $form Settings form.
     * @param SC_License    $lic  License object.
     */
    public function __construct(
        private readonly Settings_Form $form,
        private readonly SC_License $lic,
    ) {
    }

    /**
     * Registers admin menu
     */
    #[Action( tag: 'admin_menu', priority: 11 )]
    public function register_admin_menu(): void {
        \add_submenu_page(
            'mlang',
            \pll__( 'AI Translation' ),
            \pll__( 'AI Translation' ),
            'manage_options',
            'polylang-ai-automatic-translation',
            array( $this->form, 'render' ),
        );
    }

    /**
     * Registers settings fields
     */
    #[Action( tag: 'admin_init' )]
    public function register_fields(): void {
        $this->form->register_fields();
    }

    /**
     * Redirect to license settings if not licensed
     */
    #[Action( tag: 'load-languages_page_polylang-ai-automatic-translation', context: Action::CTX_ADMIN )]
    public function redirect_to_license_tab(): void {
        if ( $this->lic->is_activated() || 'license' === \xwp_fetch_get_var( 'tab', '' ) ) {
            return;
        }

        \wp_safe_redirect( \admin_url( 'admin.php?page=polylang-ai-automatic-translation&tab=license' ) );
        exit;
    }

    /**
     * Add license link to plugin action links
     *
     * @param  array<string,string> $links Plugin action links.
     * @return array<string,string>
     */
    #[Filter(
        tag: 'plugin_action_links_%s',
        priority: 10,
        context: Filter::CTX_ADMIN,
        modifiers: array( 'app.base' ),
    )]
    public function add_license_link( array $links ): array {
        $addons = array();

        if ( ! $this->lic->is_activated() ) {
            $addons['license'] = \sprintf(
                '<a href="%s">%s</a>',
                \add_query_arg(
                    array(
                        'page' => 'polylang-ai-automatic-translation',
                        'tab'  => 'license',
                    ),
                    \admin_url( 'admin.php' ),
                ),
                \__( 'Activate License', 'polylang-ai-autotranslate' ),
            );
        }
        return \array_merge( $addons, $links );
    }

    /**
     * Display license notice
     */
    #[Action( tag: 'admin_notices', context: Action::CTX_ADMIN )]
    public function display_license_notice(): void {
        if ( $this->lic->is_activated() ) {
            return;
        }

        if ( $this->is_plugin_screen() && 'license' === \xwp_fetch_get_var( 'tab', '' ) ) {
            return;
        }
        ?>
        <div class="notice notice-warning is-dismissible pllat-license-notice">
            <p>
                <?php
                \printf(
                    // Translators: %s is the link to the license settings page.
                    \esc_html__(
                        'Please <a href="%s">enter your license key</a> to activate Polylang AI Automatic Translation.',
                        'polylang-ai-autotranslate',
                    ),
                    \esc_url( \admin_url( 'admin.php?page=polylang-ai-automatic-translation&tab=license' ) ),
                );
                ?>
            </p>
        </div>
        <?php
    }

    /**
     * Render license settings output
     *
     * @param  string $dir Plugin directory.
     */
    #[Action(
        tag: 'pllat_settings_page_section_license',
        priority: 999,
        context: Action::CTX_ADMIN,
        invoke:Action::INV_PROXIED,
        args: 0,
        params: array( 'app.path' ),
    )]
    public function render_license_settings_output( string $dir ): void {
        echo '<div class="pl-[2px] pt-5 mt-5">';
        \xwp_get_template( "{$dir}templates/admin/demo-video-section.php" );
        echo '</div>';
    }

    /**
     * Handles download logs action
     */
    #[Action( tag: 'admin_init' )]
    public function handle_download_logs_action(): void {
        if ( 'pllat_download_logs' !== \xwp_fetch_req_var( 'action', '' ) ) {
            return;
        }

        $log_file  = PLLAT_PLUGIN_LOG_DIR . '/debug.log';
        $log_name  = \basename( $log_file, '.log' ) . '-' . \gmdate( 'Y-m-d_H-i-s' ) . '.log';
        $log_size  = \filesize( $log_file );
        $mime_type = \wp_check_filetype( $log_file )['type'];

        \nocache_headers();

        \header( "Content-Type: {$mime_type}" );
        \header( "Content-Length: {$log_size}" );
        \header( "Content-Disposition: attachment; filename=\"{$log_name}\"" );

        readfile( $log_file ); //phpcs:ignore
    }

    /**
     * Handles clear logs action
     */
    #[Action( tag: 'admin_init' )]
    public function handle_clear_logs_action(): void {
        if ( ! \xwp_fetch_post_var( 'pllat_clear_logs', false ) ) {
            return;
        }

        Debug_Log::clear();
    }

    /**
     * Check if the current screen is the plugin screen
     *
     * @return bool
     */
    private function is_plugin_screen(): bool {
        return 'languages_page_polylang-ai-automatic-translation' === \get_current_screen()?->id;
    }
}
