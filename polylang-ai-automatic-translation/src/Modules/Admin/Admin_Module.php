<?php
/**
 * Admin_Module class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Admin
 */

namespace PLLAT\Admin;

use XWP\DI\Decorators\Module;
use XWP_Asset_Bundle as Bundle;

/**
 * Admin module definition
 */
#[Module(
    hook: 'init',
    priority: 10,
    context: Module::CTX_ADMIN | Module::CTX_AJAX,
    handlers: array(
        Handlers\Admin_Asset_Handler::class,
        Handlers\Bulk_Translator_Page_Handler::class,
        Handlers\Settings_Ajax_Handler::class,
        Handlers\Settings_Page_Handler::class,
    ),
)]
class Admin_Module {
    /**
     * Get the module definition.
     *
     * @return array<string,mixed>
     */
    public static function configure(): array {
        return array(
            'app.assets'  => \DI\factory(
                static fn( string $app_path, string $app_url, string $app_ver ) => array(
                    'assets'   => array(
                        'admin' => array(
                            'css/admin.css',
                            array(
                                'deps' => array( 'jquery' ),
                                'src'  => 'js/admin/admin.js',
                            ),
                            array(
                                'deps' => array( 'jquery' ),
                                'src'  => 'js/admin/nav-menus.js',
                            ),
                        ),
                        'front' => array(),
                    ),
                    'base_dir' => $app_path . 'dist',
                    'base_uri' => $app_url . '/dist',
                    'id'       => 'pllat',
                    'manifest' => 'assets.php',
                    'version'  => $app_ver,
                ),
            )
                ->parameter( 'app_path', \DI\get( 'app.path' ) )
                ->parameter( 'app_url', \DI\get( 'app.url' ) )
                ->parameter( 'app_ver', \DI\get( 'app.ver' ) ),
            'app.bundle'  => \DI\factory( Bundle::make( ... ) )
                ->parameter( 'args', \DI\get( 'app.assets' ) )
                ->parameter( 'load', false ),
            Bundle::class => \DI\get( 'app.bundle' ),
        );
    }
}
