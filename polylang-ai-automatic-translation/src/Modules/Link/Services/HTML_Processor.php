<?php

namespace PLLAT\Link\Services;

use voku\helper\HtmlDomParser;
use voku\helper\SimpleHtmlDomInterface;

class HTML_Processor {
    /**
     * Get the HTML DOM parser instance for the given HTML string.
     *
     * @param  string $html HTML string to parse.
     * @return HtmlDomParser
     */
    public function get_dom( string $html ): HtmlDomParser {
        return HtmlDomParser::str_get_html( $html );
    }

    /**
     * Find all internal links in the HTML content.
     *
     * @param  HtmlDomParser $dom DOM parser instance.
     * @param  string        $home_url Home URL for the site.
     * @return array<SimpleHtmlDomInterface> Array of link elements.
     */
    public function find_internal_links( HtmlDomParser $dom, string $home_url ): array {
        // Find all home urls and root urls.
        $selector  = "a[href^='{$home_url}'],a[href^='/']";
        $dom_nodes = $dom->findMulti( $selector );

        // Only return SimpleHtmlDomInterface objects
        $links = array();
        foreach ( $dom_nodes as $dom_node ) {
            if ( ! ( $dom_node instanceof SimpleHtmlDomInterface ) ) {
                continue;
            }

            // Skip a tags with hreflang or lang attribute a.k.a language switcher links.
            if ( $dom_node->hasAttribute( 'hreflang' ) || $dom_node->hasAttribute( 'lang' ) ) {
                continue;
            }

            $links[] = $dom_node;
        }
        return $links;
    }

    /**
     * Update a link element with a new URL.
     *
     * @param  SimpleHtmlDomInterface $link Link element to update.
     * @param  string                 $new_url New URL to set.
     * @return void
     */
    public function update_link_href( SimpleHtmlDomInterface $link, string $new_url ): void {
        $link->setAttribute( 'href', $new_url );
    }

    /**
     * Get the href attribute from a link element.
     *
     * @param  SimpleHtmlDomInterface $link Link element.
     * @return string|null The href value or null if not found.
     */
    public function get_link_href( SimpleHtmlDomInterface $link ): ?string {
        return $link->getAttribute( 'href' );
    }
}
