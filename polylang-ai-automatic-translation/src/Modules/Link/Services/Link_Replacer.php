<?php
namespace PLLAT\Link\Services;

class Link_Replacer {
    /**
     * Home URL for the site, used to replace absolute URLs in the content.
     *
     * @var string
     */
    private string $home_url;

    public function __construct(
        private readonly HTML_Processor $html_processor,
        private readonly URL_Parser $url_parser,
        private readonly Content_Resolver $content_resolver,
        private readonly Link_Translation_Service $translation_service,
    ) {
        $this->home_url = \get_option( 'home' );
    }

    /**
     * Replaces the URLs in the given HTML content with the translated URLs based on the current language.
     *
     * @param  string $html HTML content containing URLs to be replaced.
     * @return string
     */
    public function replace_urls( string $html ): string {
        if ( '' === $html || ! \str_contains( $html, 'href=' ) ) {
            return $html;
        }

        $dom   = $this->html_processor->get_dom( $html );
        $links = $this->html_processor->find_internal_links( $dom, $this->home_url );

        foreach ( $links as $link ) {
            $this->replace_link_url( $link );
        }

        return $dom->html();
    }

    /**
     * Replace the URL in a single link element.
     *
     * @param  \voku\helper\SimpleHtmlDomInterface $link Link element to process.
     * @return void
     */
    private function replace_link_url( $link ): void {
        $original_url = $this->html_processor->get_link_href( $link );
        if ( ! $original_url ) {
            return;
        }

        $query_vars = $this->url_parser->extract_query_vars_from_url( $original_url );
        if ( ! $query_vars ) {
            return;
        }

        $translated_url = $this->translation_service->get_translated_url(
            $query_vars,
            $this->content_resolver,
        );
        if ( ! $translated_url || $translated_url === $original_url ) {
            return;
        }

        $final_url = $this->url_parser->preserve_url_components( $translated_url, $original_url );
        $this->html_processor->update_link_href( $link, $final_url );
    }
}
