<?php //phpcs:disable Generic.CodeAnalysis.UselessOverridingMethod.Found
/**
 * Install_Handler class file.
 *
 * @package    Automatic Translation with AI
 * @subpackage Core
 */

namespace PLLAT\Core\Handlers;

use Oblak\WP\Base_Plugin_Installer;
use SC_License;
use XWP\DI\Decorators\Action;
use XWP\DI\Decorators\Handler;

/**
 * Handles plugin installation.
 */
#[Handler( tag: 'plugins_loaded', priority: 900 )]
class Install_Handler extends Base_Plugin_Installer {
    /**
     * Make constructor public.
     */
    public function __construct() {
        static::$instance[ static::class ] = $this;
        parent::__construct();
    }

    /**
     * Initialize the handler.
     *
     * @return void
     */
    #[Action( tag: 'plugins_loaded', priority: 1000 )]
    public function init() {
        parent::init();
    }

    /**
     * Create the options for the plugin.
     *
     * @return void
     */
    public function create_options() {
        $option = \get_option( 'polylangaiautomatictranslation_license_options', false );

        if ( ! $option ) {
            \xwp_delete_notice( 'surecart_pllat_license_invalid' );
            return;
        }

        $license = new SC_License();
        $license->set_props(
            array(
                'activated'     => false,
                'activation_id' => $option['sc_activation_id'],
                'license_id'    => $option['sc_license_id'],
                'license_key'   => $option['sc_license_key'],
                'registered'    => false,
                'status'        => 'active',
            ),
        );

        $license->save( 'pllat' );

        \as_schedule_single_action(
            \time() + 60,
            'surecart_pllat_license_migrate',
            array(
                'id'     => $license->get_id(),
                'option' => 'polylangaiautomatictranslation_license_options',
            ),
            'pllat',
            true,
        );
    }

    /**
     * Set the defaults for the plugin.
     *
     * @return void
     */
    protected function set_defaults() {
        $this->tr_name = static fn() => \__(
            'Polylang Automatic Translation with AI',
            'polylang-ai-autotranslate',
        );
        $this->slug    = 'pllat';
        $this->version = \PLLAT_PLUGIN_VERSION;
    }
}
