<?php
/**
 * Core_Module class file.
 *
 * @package    Polylang AI Automatic Translation
 * @subpackage Core
 */

namespace PLLAT\Core;

use XWP\DI\Decorators\Module;

/**
 * Core module declaration.
 */
#[Module( hook: 'pll_init', priority: 1, handlers:array( Handlers\Install_Handler::class ) )]
class Core_Module {
    /**
     * Get the module dependencies.
     *
     * @return array<string,mixed>
     */
    public static function configure(): array {
        return array(
            'app.activated'   => \DI\factory( array( \SC_License::class, 'is_activated' ) ),
            'app.licensed'    => \DI\factory( array( \SC_License::class, 'is_registered' ) ),
            'surecart.config' => \DI\factory(
                static fn( $name ) => array(
                    'id'    => 'pllat',
                    'name'  => $name,
                    'page'  => array(
                        'action'   => 'pllat_settings_page_section_license',
                        'location' => 'action',
                    ),
                    'slug'  => 'polylang-ai-automatic-translation',
                    'token' => 'pt_HsZLYz3DNSDpDPVkc638Qkde',
                    'type'  => 'plugin',
                ),
            )->parameter( 'name', \DI\get( 'app.name' ) ),
        );
    }
}
