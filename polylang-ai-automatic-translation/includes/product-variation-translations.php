<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Post;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;

class Product_Variation_Translations {

    const BLOCK_META_KEYS = ['_pllat_is_processed', 'pllat_is_processed'];

    private static $instance = null;
    private $language_manager;

    public function __construct() {

        $this->language_manager = xwp_app( 'pllat' )->get( Language_Manager::class );

        add_action('added_post_meta', [$this, 'block_initial_meta_data_set'], 10, 3);
        add_action('woocommerce_new_product_variation', [$this, 'handle_product_created']);
        add_action('woocommerce_before_product_object_save', [$this, 'before_product_variation_save'], 99);
        add_action('woocommerce_after_product_object_save', [$this, 'after_product_variation_save'], 99);
    }

    /**
     * Sets a transient with meta keys that should initially be blocked when polylang copies them during creation
     */
    public function handle_product_created($variation_id) {
        set_transient('block_initial_meta_data_' . $variation_id, self::BLOCK_META_KEYS, 60 * 60 * 24);
    }

    /**
     * Blocks the initial meta data set by polylang when creating a product variation
     */
    public function block_initial_meta_data_set($meta_id, $object_id, $meta_key) {

        // Check if meta key is in block list
        if (!in_array($meta_key, self::BLOCK_META_KEYS)) 
            return;

        // Get meta data that should be blocked from the transient
        $metadata = get_transient('block_initial_meta_data_' . $object_id);

        // If meta data is not empty and the meta key is in the list
        if (!empty($metadata) && in_array($meta_key, $metadata)) {

            // Delete meta data
            delete_post_meta($object_id, $meta_key);

            // Remove meta key from transient so other meta keys remain
            set_transient('block_initial_meta_data_' . $object_id, array_diff($metadata, [$meta_key]), 60 * 60 * 24);
        }
    }

    /**
     * Saves the pre post meta data before the product variation is saved
     */
    public function before_product_variation_save($product) {

        // Only product variations
        if (!$product->is_type('variation')) return;

        $post_id = $product->get_id();

        // Get post meta before saving
        $post_meta = Helpers::get_flatten_post_meta($post_id);

        // Store before post meta in transient to compare after saving
        set_transient('pllat_pre_post_meta_' . $post_id, $post_meta, 60 * 60 * 24);
    }

    /**
     * Saves the changes between the post meta data before and after updating
     */
    public function after_product_variation_save($product) {
        
        // Only product variations
        if (!$product->is_type('variation')) return;

        $post_id = $product->get_id();

        // Get post meta before saving
        $pre_post_meta = get_transient('pllat_pre_post_meta_' . $post_id);
        if (empty($pre_post_meta)) return;

        // Get post meta after saving
        $post_meta = Helpers::get_flatten_post_meta($post_id);
        if (empty($post_meta)) return;

        $available_fields = Post_Meta_Translation::get_available_fields_for($post_id);

        // Compare the post meta data before and after updating
        $changes = Helpers::get_changed_fields($pre_post_meta, $post_meta, $available_fields);

        if (empty($changes)) return;

        $languages = $this->language_manager->get_available_languages(true);
        $post_manager = Helpers::get_translatable_post_manager($post_id);
        foreach ($languages as $language) {
            $post_manager->add_meta_to_queue($language, $changes);
        }

        // Delete pre meta transient
        delete_transient('pllat_pre_post_meta_' . $post_id);
    }

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

}

Product_Variation_Translations::get_instance();