<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Terms\Translatable_Term_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Terms\Translatable_Term;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator_Factory;

class Term_Translation_Changes_Handler {

    private static $instance = null;
    private $language_manager;

    private function __construct() {
        $this->language_manager = xwp_app( 'pllat' )->get( Language_Manager::class );
        add_action('edit_terms', [$this, 'handle_pre_insert_term'], 10, 2);
        add_action('edited_term', [$this, 'handle_edited_term'], 10, 3);
        add_action('edited_term', [$this, 'handle_after_insert_term'], 10, 3);
        add_action('save_term', [$this, 'tidy_on_save'], 10, 1);
    }

    public function handle_pre_insert_term($term_id, $taxonomy) {
        
        // Get term data before it is updated as an array
        $term = get_term_by('id', $term_id, $taxonomy, ARRAY_A);

        // Store the term data in a transient to compare it with the data after the term is updated
        set_transient('pllat_pre_term_' . $term_id, $term, 60 * 60 * 24);

        // Save term meta data before updating to compare after updating
        $this->transient_term_meta($term_id);
    }

    public function handle_edited_term($term_id, $tt_id, $taxonomy) {

        // Get keys of fields that are available for translation
        $available_fields = Term_Translation::get_available_fields_for($term_id);

        // Get term data before and after updating
        $before_term = get_transient('pllat_pre_term_' . $term_id);
        $after_term = get_term_by('id', $term_id, $taxonomy, ARRAY_A);

        if (!$before_term) return;

        // Compare the term data before and after updating
        $changes = Helpers::get_changed_fields($before_term, $after_term, $available_fields);

        // If something has been changed
        if (!empty($changes)) {
            $term_manager = Helpers::get_translatable_term_manager($term_id);

            $available_languages = $this->language_manager->get_available_languages(true);
            foreach ($available_languages as $language) {
                $term_manager->add_to_queue($language, $changes);
            }
        }
    }

    public function handle_after_insert_term($term_id, $tt_id, $taxonomy) {

        // Get available fields
        $available_fields = Term_Meta_Translation::get_available_fields_for($term_id);

        // Get term meta data before updating
        $pre_term_meta = get_transient('pllat_pre_term_meta_' . $term_id);
        if (!$pre_term_meta) return;

        // Get term meta data after updating
        $term_meta = Helpers::get_flatten_term_meta($term_id);

        // Compare the term meta data before and after updating
        $changes = Helpers::get_changed_fields($pre_term_meta, $term_meta, $available_fields);

        if (empty($changes)) return;

        $term_manager = Helpers::get_translatable_term_manager($term_id);

        $available_languages = $this->language_manager->get_available_languages();
        foreach ($available_languages as $language) {
            $term_manager->add_meta_to_queue($language, $changes);
        }

        // Delete the transient
        delete_transient('pllat_pre_term_meta_' . $term_id);
    }

    public function tidy_on_save($term_id) {
        $term_manager = Helpers::get_translatable_term_manager($term_id);
        $term_manager->tidy_queue();
    }

    private function transient_term_meta($term_id) {
        $term_meta = Helpers::get_flatten_term_meta($term_id);
        set_transient('pllat_pre_term_meta_' . $term_id, $term_meta, 60 * 60 * 24);
    }

    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}

Term_Translation_Changes_Handler::get_instance();

