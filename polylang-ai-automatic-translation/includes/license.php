<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

class License_Manager {

    private static $instance = null;
    private $client;

    private function __construct() {

		$this->client = Helpers::get_license_client();
        
        add_action('admin_init', [$this, 'check_license']);
        add_action('admin_notices', [$this, 'display_license_notice']);
        add_filter('plugin_action_links_' . plugin_basename(PLLAT_PLUGIN_FILE), [$this, 'add_license_link']);
		add_action('pllat_license_settings_output', [$this, 'render_license_settings_output']);
    }

	public function render_license_settings_output() {
		$this->client->settings()->settings_output();
        echo '<div class="pl-[2px] pt-5">';
            include_once PLLAT_PLUGIN_DIR . 'templates/admin/demo-video-section.php';
        echo '</div>';
	}

    public function check_license() {

        // Check if we're on our settings page
        $page = $_GET['page'] ?? '';
        $tab = $_GET['tab'] ?? '';
        
        if ($page === 'polylang-ai-automatic-translation' && $tab !== 'license' && !$this->is_license_valid()) {
            wp_redirect(admin_url('admin.php?page=polylang-ai-automatic-translation&tab=license'));
            exit;
        }
    }

    public function display_license_notice() {

        if ($this->is_license_valid()) {
            return;
        }

        $screen = get_current_screen();
        if ($screen->id === 'languages_page_polylang-ai-automatic-translation' && isset($_GET['tab']) && $_GET['tab'] === 'license') {
            return;
        }

        echo '<div class="notice notice-warning is-dismissible pllat-license-notice">';
        echo '<p>' . sprintf(
            __('Please <a href="%s">enter your license key</a> to activate Polylang AI Automatic Translation.', 'polylang-ai-autotranslate'),
            admin_url('admin.php?page=polylang-ai-automatic-translation&tab=license')
        ) . '</p>';
        echo '</div>';
    }

    public function add_license_link($links) {
        $license_link = sprintf(
            '<a href="%s">%s</a>',
            admin_url('admin.php?page=polylang-ai-automatic-translation&tab=license'),
            $this->is_license_valid() ? __('License', 'polylang-ai-autotranslate') : __('Activate License', 'polylang-ai-autotranslate')
        );
        array_unshift($links, $license_link);
        return $links;
    }

    public function is_license_valid() {
        return $this->client->license()->is_valid();
    }

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}

License_Manager::get_instance();