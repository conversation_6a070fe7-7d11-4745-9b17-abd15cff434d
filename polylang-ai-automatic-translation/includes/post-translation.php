<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

class Post_Translation_Handler {

    private static $instance;

    private function __construct() {
        add_action('pllat_post_translation_input', [$this, 'filter_post_translation_input'], 10, 4);
    }

    public function filter_post_translation_input($translation_input, $entity, $language, $args) {
        if (!empty($args['force_mode'])) {
            $translation_input = array_filter($translation_input, fn($key) => $key !== 'post_name');
        }
        return $translation_input;
    }

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}

Post_Translation_Handler::get_instance();
