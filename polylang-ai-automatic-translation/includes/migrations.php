<?php
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Migrations\Migration_Factory;

class Migrations_Handler {

    const MIGRATIONS = [
        '2.2.8'
    ];

    private static $instance = null;

    private $language_manager;

    private function __construct() {
        $this->language_manager = xwp_app( 'pllat' )->get( Language_Manager::class );
        $this->handle_test_migration();
        add_action('upgrader_process_complete', [$this, 'check_version_and_run_migrations'], 10, 2);
    }

    public function handle_test_migration() {
        if (empty($_GET['pllat_test_migration'])) return;
        $version = $_GET['pllat_test_migration'];
        $this->run_migration($version);
    }

    public function check_version_and_run_migrations($upgrader_object, $options) {
        if ($options['action'] == 'update' && $options['type'] == 'plugin') {
            $our_plugin = plugin_basename(PLLAT_PLUGIN_FILE);
            if (!empty($options['plugins']) && in_array($our_plugin, $options['plugins'])) {
                $this->run_migrations();
            }
        }
    }

    private function run_migrations() {
        $current_version = $this->get_migration_version();
        foreach (self::MIGRATIONS as $version) {
            if (version_compare(PLLAT_PLUGIN_VERSION, $version, '>=') && 
                version_compare($current_version, $version, '<')) {
                $this->run_migration($version);
                $this->store_migration_version($version);
            }
        }
        // Update to the current plugin version after all migrations
        $this->store_migration_version(PLLAT_PLUGIN_VERSION);
    }

    private function run_migration(string $version) {
        $migration = Migration_Factory::create_migration($version);
        if ($migration) {
            $migration->run();
        }
    }

    private function store_migration_version() {
        update_option('_pllat_migration_version', PLLAT_PLUGIN_VERSION);
    }

    private function get_migration_version() {
        return get_option('_pllat_migration_version', '0');
    }

    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
}
    
Migrations_Handler::get_instance();
