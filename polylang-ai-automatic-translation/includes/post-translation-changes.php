<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Translatable_Post_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Translatable_Post;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator_Factory;

class Post_Translation_Changes_Handler {

    private static $instance = null;

    private $language_manager;

    private function __construct() {

        $this->language_manager = xwp_app( 'pllat' )->get( Language_Manager::class );

        add_action('wp_after_insert_post', [$this, 'handle_after_insert_post'], 99, 3);
        add_action('post_updated', [$this, 'handle_post_updated'], 99, 3);
        add_action('save_post', [$this, 'tidy_on_save'], 10, 1);
    }

    public function handle_post_updated($post_id, $post_after, $post_before) {

        // Get keys of fields that are available for translation
        $available_fields = Post_Translation::get_available_fields_for($post_id);

        // Convert post objects to arrays
        $before_array = $post_before->to_array();
        $after_array = $post_after->to_array();
        
        // Compare the post data before and after updating
        $changes = Helpers::get_changed_fields($before_array, $after_array, $available_fields);

        // If something has been changed
        if (!empty($changes)) {

            $post_manager = Helpers::get_translatable_post_manager($post_id);

            $available_languages = $this->language_manager->get_available_languages(true);
            foreach ($available_languages as $language) {
                $post_manager->add_to_queue($language, $changes);
            }
        }

        // Save post meta data before updating to compare after updating
        $this->transient_post_meta($post_id);
    }

    /**
     * Saves the changes between the post meta data before and after updating
     */
    public function handle_after_insert_post($post_id, $post, $update) {

        if (!$update) return;

        // Get available fields
        $available_fields = Post_Meta_Translation::get_available_fields_for($post_id);

        // Get post meta data before updating
        $pre_post_meta = get_transient('pllat_pre_post_meta_' . $post_id);
        if (!$pre_post_meta) return;

        // Get post meta data after updating
        $post_meta = Helpers::get_flatten_post_meta($post_id);

        // Compare the post meta data before and after updating
        $changes = Helpers::get_changed_fields($pre_post_meta, $post_meta, $available_fields);

        if (empty($changes)) return;

        $post_manager = Helpers::get_translatable_post_manager($post_id);

        $available_languages = $this->language_manager->get_available_languages();
        foreach ($available_languages as $language) {
            $post_manager->add_meta_to_queue($language, $changes);
        }

        // Delete the transient
        delete_transient('pllat_pre_post_meta_' . $post_id);
    }

    public function tidy_on_save($post_id) {
        $post_manager = Helpers::get_translatable_post_manager($post_id);
        $post_manager->tidy_queue();
    }

    /**
     * Saves the post meta data before updating via transient
     */
    private function transient_post_meta($post_id) {
        $post_meta = Helpers::get_flatten_post_meta($post_id);
        set_transient('pllat_pre_post_meta_' . $post_id, $post_meta, 60 * 60 * 24);
    }

    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

} 

Post_Translation_Changes_Handler::get_instance();