<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Translatable_Post;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Prompt_Handler;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\OpenAI\Token_Encoder;
use EPIC_WP\Polylang_Automatic_AI_Translation\Constants;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Post_Translation_Background_Processor;

class Single_Post_Translation_Handler extends Abstract_Single_Translation_Handler {

    private static $instance = null;
    private $background_processor;
    
    private function __construct() {

        parent::__construct();

        $this->background_processor = new Post_Translation_Background_Processor();

        $this->register_ajax_hooks();
        $this->register_ui_hooks();
        $this->register_hooks();
    }

    /**
     * Gets the translation context
     * 
     * @return string
     */
    protected function get_translation_context() {
        return 'post';
    }

    private function register_ajax_hooks() {
        add_action('wp_ajax_pllat_queue_post_translations', [$this, 'handle_ajax_queue_post_translations']);
        add_action('wp_ajax_pllat_process_post_translation', [$this, 'handle_ajax_process_post_translation']);
        add_action('wp_ajax_pllat_check_post_translation_status', [$this, 'handle_ajax_check_translation_status']);
        add_action('wp_ajax_pllat_cancel_post_translations', [$this, 'handle_ajax_cancel_post_translations']);
        add_action('wp_ajax_pllat_clear_translation_error', [$this, 'handle_ajax_clear_translation_error']);
        add_action('wp_ajax_pllat_clear_post_translation_error', [$this, 'handle_ajax_clear_translation_error']);
        add_action('wp_ajax_pllat_get_post_translation_errors', [$this, 'handle_ajax_get_translation_errors']);
    }

    private function register_ui_hooks() {
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
        add_action('add_meta_boxes', [$this, 'add_meta_boxes']);
    }

    private function register_hooks() {
        add_filter('pll_copy_post_metas', [$this, 'exclude_custom_fields_from_copy'], 10, 2);
        add_action('save_post', [$this, 'save_post_meta_fields'], 10, 3);
    }

    /**
     * Handles AJAX request to queue post translations
     */
    public function handle_ajax_queue_post_translations() {

        try {
            
            if (empty($_POST['id'])) {
                wp_send_json_error(['message' => pll__('No post ID provided.')]);
                wp_die();
            }
            
            if (empty($_POST['languages']) || !is_array($_POST['languages'])) {
                wp_send_json_error(['message' => pll__('No languages provided.')]);
                wp_die();
            }
            
            $post_id = (int) $_POST['id'];
            $languages = $_POST['languages'];
                        
            $queued_languages = [];
            $args = [];
            
            // Check whether there are additional ai instructions set
            if (!empty($_POST['instructions'])) {
                $args['additional_instructions'] = sanitize_textarea_field($_POST['instructions']);
            }

            $post_manager = Helpers::get_translatable_post_manager($post_id);
            $translatable_post = $post_manager->get_translatable_entity();

            // Skip when post is excluded from translation
            if ($translatable_post->is_excluded_from_translation()) {
                wp_send_json([
                    'message' => pll__('Post is excluded from translation so is skipped.')
                ]);
                wp_die();
            }

            // Clear any existing errors for this post
            $this->background_processor->clear_latest_error($post_id);
            $this->background_processor->clear_translation_timestamps($post_id);

            $force_mode = isset($_POST['force']) && $_POST['force'] === 'true';

            // Make sure all missing languages are queued
            $post_manager->populate_queue();
            
            // Make sure the queue only contains active languages
            $post_manager->tidy_queue();

            // Get only the languages that are in the queue
            $queue_languages = $force_mode ? $languages : $translatable_post->get_queue_languages();

            // Make sure the queue only contains requested languages
            $queue_languages = array_intersect($queue_languages, $languages);

            // Fire action when all languages are queued for a post
            do_action('pllat_single_post_translation_languages_queued', intval($post_id), $queue_languages);
            
            wp_send_json_success([
                'translation_queue' => $queue_languages
            ]);
            
        } catch (\Throwable $e) {
            Helpers::log($e->getMessage() . ' - ' . $e->getTraceAsString(), 'error');
            wp_send_json_error(['message' => $e->getMessage()]);
        }
        wp_die();
    }

    /**
     * Handles AJAX request to process post translation by scheduling an action
     */
    public function handle_ajax_process_post_translation() {

        try {
            if (empty($_POST['id'])) {
                wp_send_json_error(['message' => pll__('No post ID provided.')]);
                wp_die();
            }
    
            if (empty($_POST['language_slug'])) {
                wp_send_json_error(['message' => pll__('No language slug provided.')]);
                wp_die();
            }

            $args = [];
            $post_id = $_POST['id'];
            $language_slug = $_POST['language_slug'];    

            // Check whether there are additional ai instructions set
            if (!empty($_POST['instructions'])) {
                $args['additional_instructions'] = sanitize_textarea_field($_POST['instructions']);
            } 

            // Check whether there is ai website context set
            $website_context = Settings::get_website_ai_context();
            if (!empty($website_context)) {
                $args['website_context'] = $website_context;
            }

            // Add force mode to args to manage later in the post manager when processing queue item
            $force_mode = isset($_POST['force']) && $_POST['force'] === 'true';
            if ($force_mode) {
                $args['force_mode'] = true;
            }

            $action_id = $this->background_processor->schedule_post_translation(
                $post_id,
                $language_slug,
                $args,
            );

            if (!$action_id) {
                throw new \Exception('Failed to schedule translation.');
            }

            wp_send_json_success([
                'scheduled' => $language_slug,
            ]);

        } catch (\Throwable $e) {
            Helpers::log($e->getMessage() . ' - ' . $e->getTraceAsString(), 'error');
            wp_send_json_error(['message' => $e->getMessage()]);
        }
        wp_die();
    }

    /**
     * Handles AJAX request to check the translation status
     */
    public function handle_ajax_check_translation_status() {
        
        if (empty($_POST['id'])) {
            wp_send_json_error(['message' => pll__('No post ID provided.')]);
            wp_die();
        }
        
        $post_id = (int) $_POST['id'];
        $status = $this->background_processor->get_post_translation_status($post_id);

        wp_send_json_success([
            'status' => $status['status'],
            'pending_languages' => $status['pending_languages'],
            'processing_languages' => $status['processing_languages'],
            'processing_timestamps' => $status['processing_timestamps'],
            'errors' => $status['errors'],
            'has_error' => !empty($status['errors']),
        ]);
        
        wp_die();
    }

    /**
     * Handles AJAX request to cancel post translations
     */
    public function handle_ajax_cancel_post_translations() {

        if (empty($_POST['id'])) {
            wp_send_json_error(['message' => pll__('No post ID provided.')]);
            wp_die();
        }
        
        try {
            $post_id = $_POST['id'];
            $cancelled_count = $this->background_processor->cancel_post_translations($post_id);
            
            wp_send_json_success([
                'cancelled_count' => $cancelled_count,
                'message' => sprintf(pll__('Cancelled %d translation tasks.'), $cancelled_count)
            ]);
        } catch (\Throwable $e) {
            Helpers::log($e->getMessage() . ' - ' . $e->getTraceAsString(), 'error');
            wp_send_json_error(['message' => $e->getMessage()]);
        }
        wp_die();
    }

    /**
     * Handles AJAX request to clear translation error
     */
    public function handle_ajax_clear_translation_error() {

        if (empty($_POST['id'])) {
            wp_send_json_error(['message' => pll__('No post ID provided.')]);
            wp_die();
        }
        
        $post_id = (int) $_POST['id'];
        
        // Actually clear the error using the background processor
        $this->background_processor->clear_latest_error($post_id);
        
        wp_send_json_success(['message' => pll__('Error cleared successfully.')]);
        wp_die();
    }

    /**
     * Handles AJAX request to get translation errors
     */
    public function handle_ajax_get_translation_errors() {
        if (empty($_POST['id'])) {
            wp_send_json_error(['message' => pll__('No post ID provided.')]);
            wp_die();
        }
        
        $post_id = (int) $_POST['id'];
        $error = $this->background_processor->get_latest_error($post_id);
        
        wp_send_json_success([
            'has_error' => !empty($error),
            'error_message' => $error
        ]);
        
        wp_die();
    }

    /**
     * Renders post translation metabox
     */
    public function render_translator_metabox() {

        global $post;

        $post_id = $post->ID;
        $translatable_post = new Translatable_Post($post_id, $this->language_manager);
        $queue = $translatable_post->get_queue();

        $exclude_from_translation = $translatable_post->is_excluded_from_translation();

        // The active translation API and key
        $active_translation_api = Settings::get_active_translation_api();
        $active_translation_api_key = Settings::get_active_translation_api_key($active_translation_api);

        $current_language = pll_get_post_language($post_id);
        $available_languages = $this->language_manager->get_available_languages();
        $available_languages = array_diff($available_languages, [$current_language]);
        $included_languages = array_keys($queue);
        $all_checked = count($included_languages) === count($available_languages);

        $type = 'post';
        $entity = $post->post_type;
        $id = $post_id;

        $is_debug_mode = Settings::is_debug_mode();

        if ($is_debug_mode) {
            $available_fields = Post_Translation::get_available_fields_for($post_id);
            $available_meta_fields = Post_Meta_Translation::get_available_fields_for($post_id);
        }

        // Load the single post translation template to display the metabox
        $template = PLLAT_PLUGIN_DIR . 'templates/admin/single-translation.php';
        if (file_exists($template)) {
            include $template;
        }
    }

    /**
     * Adds metaboxes to post
     */
    public function add_meta_boxes() {
        add_meta_box(
            'pllat_translator_metabox', 
            'AI automatic translation',
            [$this, 'render_translator_metabox'],
            Helpers::get_active_post_types(),
            'normal',
            'high'
        );
    }

    /**
     * Saves the post meta fields
     * 
     * @param int $post_id
     */
    public function save_post_meta_fields($post_id) {
        $post_manager = Helpers::get_translatable_post_manager($post_id);
        if (!empty($_POST['pllat_exclude_from_translation'])) {
            $post_manager->update_exclude_from_translation(true);
        } else {
            $post_manager->update_exclude_from_translation(false);
        }
    }

    /**
     * Excludes specific custom fields from polylang copy
     * 
     * @param array $metas
     * @return array
     */
    public function exclude_custom_fields_from_copy($metas) {
        $excluded_meta = Constants::get_excluded_copy_meta_fields();
        return array_diff($metas, $excluded_meta);
    }

    /**
     * Enqueue admin scripts and styles
     * 
     * Only enqueue on post edit pages
     */
    public function enqueue_admin_scripts() {
        $screen = get_current_screen();
        if (empty($screen) || !in_array($screen->base, ['post'])) {
            return;
        }
        $this->enqueue_translation_admin_scripts();
    }

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}

Single_Post_Translation_Handler::get_instance();