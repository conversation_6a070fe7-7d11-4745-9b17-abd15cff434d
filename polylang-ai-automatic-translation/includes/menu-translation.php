<?php //phpcs:disable
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;

class Menu_Translation {

    private static $instance = null;
    private $language_manager;

    private function __construct() {

        $this->language_manager = xwp_app( 'pllat' )->get( Language_Manager::class );

        add_action('wp_ajax_pllat_get_translation_section_html', [$this, 'handle_ajax_get_translation_section_html']);
        add_action('wp_update_nav_menu', [$this, 'handle_menu_update']);
    }

    public function handle_ajax_get_translation_section_html() {

        if (!empty($_GET['object_id'])) {
            $menu_id = $_GET['object_id'];
        } else {
            wp_die();
        }

        // Check if there are menus anyway
        $menus = wp_get_nav_menus();
        if (empty($menus)) {
            wp_die();
        }

        // Check if menu has already items
        $menu_items = wp_get_nav_menu_items($menu_id);
        if (!empty($menu_items)) {
            wp_die();
        }

        // Remove current menu from the list
        $menus = array_filter($menus, function($menu) use ($menu_id) {
            return $menu->term_id != $menu_id;
        });

        // Get all available languages
        $available_languages = $this->language_manager->get_available_languages();
        if (empty($available_languages)) {
            wp_die();
        }

        // Get the name and slug of the languages
        $available_languages = array_map(function($language) {
            $language_data = $this->language_manager->get_language_data($language);
            return [
                'slug' => $language,
                'name' => $language_data['name'],
            ];
        }, $available_languages);

        // Load html
        include PLLAT_PLUGIN_DIR . 'templates/admin/nav-menus-translate.php';
        wp_die();
    }

    public function handle_menu_update($menu_id) {

        if (empty($_POST['menu_copy_source']) || empty($_POST['menu_copy_language'])) {
            return;
        }

        $menu_source = $_POST['menu_copy_source'];
        $menu_language = $_POST['menu_copy_language'];

        // Get the menu items
        $menu_items = wp_get_nav_menu_items($menu_source);
        if (empty($menu_items)) {
            return;
        }

        // Create the new menu
        $menu = wp_get_nav_menu_object($menu_id);
        if (empty($menu)) {
            return;
        }

        $new_menu_ids = [];

        foreach ($menu_items as $menu_item) {
            $type = $menu_item->type;
            $menu_item_parent = $menu_item->menu_item_parent;
            $menu_item_id = $menu_item->ID;
            $object_id = $menu_item->object_id;
            $new_object_id = null;

            $menu_item_data = [
                'menu-item-status' => 'publish',
                'menu-item-type' => $menu_item->type,
            ];

            switch ($type) {
                case 'post_type' :
                    $translations = pll_get_post_translations($object_id);
                    if (!empty($translations[$menu_language])) {
                        $new_object_id = $translations[$menu_language];
                        $post = get_post($new_object_id);
                        if (empty($post)) {
                            continue 2;
                        }
                        $menu_item_data = array_merge($menu_item_data, [
                            'menu-item-title' => $post->post_title,
                            'menu-item-object-id' => $new_object_id,
                            'menu-item-object' => $menu_item->object
                        ]);
                    } else {
                        continue 2;
                    }
                    break;
                case 'taxonomy' :
                    $translations = pll_get_term_translations($object_id);
                    if (!empty($translations[$menu_language])) {
                        $new_object_id = $translations[$menu_language];
                        $term = get_term($new_object_id, $menu_item->taxonomy);
                        if (empty($term) || is_wp_error($term)) {
                            continue 2;
                        }
                        $menu_item_data = array_merge($menu_item_data, [
                            'menu-item-title' => $term->name,
                            'menu-item-object-id' => $new_object_id,
                            'menu-item-object' => $menu_item->object
                        ]);
                    } else {
                        continue 2;
                    }
                    break;
                case 'custom' :
                    $new_object_id = $menu_item->ID;
                    $menu_item_data = array_merge($menu_item_data, [
                        'menu-item-title' => $menu_item->title,
                        'menu-item-url' => $menu_item->url
                    ]);
                    break;
                default:
            }

            // Set parent id
            if ($menu_item_parent != 0 && isset($new_menu_ids[$menu_item_parent])) {
                $menu_item_data['menu-item-parent-id'] = $new_menu_ids[$menu_item_parent];
            }

            // Create new menu item
            $db_id = wp_update_nav_menu_item($menu_id, 0, $menu_item_data);
            if (!is_wp_error($db_id)) {
                $new_menu_ids[$menu_item_id] = $db_id;
            }
        }
    }

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

}

Menu_Translation::get_instance();
