<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;

class Internal_Link_Replacer {

    private static $instance = null;

    public function __construct() {
        if (!is_admin() && Features::is_url_replacement_enabled()) {
            // Filter the main content
            add_filter('the_content', [$this, 'replace_urls'], 20);
            // Filter navigation menus
            add_filter('wp_nav_menu_items', [$this, 'replace_urls'], 20);
            // Replace urls in elementor templates
            add_filter('elementor/frontend/the_content', [$this, 'replace_urls'], 20);
        }
    }

    public function replace_urls($content) {
        if (empty($content) || !is_string($content)) {
            return $content;
        }

        $urls = $this->extract_urls_from_html($content);
        if (empty($urls)) {
            return $content;
        }

        $current_lang = pll_current_language();
        if (!$current_lang) {
            return $content;
        }
        
        foreach ($urls as $url_data) {
            $content = $this->process_url($url_data, $current_lang, $content);
        }

        return $content;
    }

    /**
     * Process a single URL and return the updated content
     */
    private function process_url($url_data, $current_lang, $content) {
        $original_url = $url_data['original'];
        $full_tag = $url_data['full_tag'];
        
        // Parse the original URL to preserve query string
        $parsed_url = parse_url($original_url);
        $path = $parsed_url['path'] ?? '';
        
        // Get original query parameters
        $original_query = $this->extract_query_params($original_url);
        
        // Retain hashtags
        $fragment = isset($parsed_url['fragment']) ? '#' . $parsed_url['fragment'] : '';
     
        // Normalize URL for processing (only the path)
        $normalized_url = $this->normalize_url($path);
        
        // Try post translation first
        $translated_url = $this->get_translated_post_url($normalized_url, $current_lang);
        // If no post translation found, try term translation
        if (!$translated_url) {
            $translated_url = $this->get_translated_term_url($normalized_url, $current_lang);
        }
    
        // If we found a translation, replace it in the content
        if ($translated_url && $translated_url !== $path) {
            // Parse the translated URL
            $parsed_translated_url = parse_url($translated_url);
            $translated_path = $parsed_translated_url['path'] ?? '';
            
            // Get translated URL query parameters
            $translated_query = $this->extract_query_params($translated_url);
            
            // Combine URLs with query parameters
            $new_url = $this->combine_url_query_params($translated_path, $original_query, $translated_query);
            
            // Add fragment
            $new_url .= $fragment;
            
            $new_tag = str_replace($original_url, $new_url, $full_tag);
            return str_replace($full_tag, $new_tag, $content);
        }
        return $content;    
    }

    /**
     * Combine query parameters from two URLs, with original parameters taking precedence
     * 
     * @param string $base_url The base URL to add parameters to
     * @param array $original_query Original query parameters
     * @param array $translated_query Translated URL query parameters
     * @return string The combined URL with merged query parameters
     */
    private function combine_url_query_params($base_url, $original_query, $translated_query) {
        // Start with the base URL
        $new_url = rtrim($base_url, '/');
        
        // Merge query parameters, original takes precedence
        $combined_query = array_merge($translated_query, $original_query);
        
        // Use WordPress function to add query parameters
        if (!empty($combined_query)) {
            $new_url = add_query_arg($combined_query, $new_url);
        }
        
        return $new_url;
    }

    /**
     * Extract query parameters from a URL
     * 
     * @param string $url The URL to extract query parameters from
     * @return array Associative array of query parameters
     */
    private function extract_query_params($url) {
        $parsed_url = parse_url($url);
        $query_params = [];
        
        if (isset($parsed_url['query'])) {
            parse_str($parsed_url['query'], $query_params);
        }
        
        return $query_params;
    }

    /**
     * Get translated URL for a post
     */
    private function get_translated_post_url($normalized_url, $current_lang) {
        $post_id = $this->get_post_id_from_url($normalized_url);
        if (!$post_id) {
            return false;
        }

        $translated_post_id = pll_get_post($post_id, $current_lang);
        if (!$translated_post_id) {
            return false;
        }

        return get_permalink($translated_post_id);
    }

    /**
     * Get translated URL for a term
     */
    private function get_translated_term_url($normalized_url, $current_lang) {
        $term_data = $this->get_term_from_url($normalized_url);
        if (!$term_data) {
            return false;
        }

        $translations = pll_get_term_translations($term_data['term_id']);
        if (empty($translations)) {
            return false;
        }

        $current_term_id = $translations[$current_lang] ?? null;
        if (!$current_term_id) {
            return false;
        }

        $translated_url = get_term_link($current_term_id, $term_data['taxonomy']);
        return is_wp_error($translated_url) ? false : $translated_url;
    }

    /**
     * Extract relevant URLs from HTML to replace with the translated URLs
     *
     * @param string $html The HTML content
     * @return array Array of URLs found in the HTML
     */
    private function extract_urls_from_html($html) {
        $urls = [];
        $current_site_url = site_url();
        $domain = parse_url($current_site_url, PHP_URL_HOST);
        
        // Match all links in the HTML
        $pattern = '/<a\s[^>]*href=[\'"]([^\'"]+)[\'"][^>]*>/i';
        if (preg_match_all($pattern, $html, $matches)) {
            foreach ($matches[1] as $key => $url) {
                // Skip if empty
                if (empty($url)) {
                    continue;
                }
                
                // Skip external links or non-http/https links
                if (strpos($url, 'http') !== 0 && !preg_match('/^\/[^\/]/', $url)) {
                    continue;
                }
                
                // Skip links that don't contain our domain for absolute URLs
                if (strpos($url, 'http') === 0) {
                    $url_domain = parse_url($url, PHP_URL_HOST);
                    // Check if it's not our domain or a language subdomain of our domain
                    if (!$url_domain || (
                        $url_domain !== $domain && 
                        !preg_match('/\.[^.]+\.' . preg_quote(preg_replace('/^www\./', '', $domain), '/') . '$/', $url_domain) &&
                        !preg_match('/^[^.]+\.' . preg_quote(preg_replace('/^www\./', '', $domain), '/') . '$/', $url_domain)
                    )) {
                        continue;
                    }
                }
                
                // Skip URLs of common non-translatable resources
                if (preg_match('/\.(jpg|jpeg|png|gif|svg|webp|mp4|mp3|webm|pdf|zip|doc|docx|xls|xlsx|css|js)(\?.*)?$/i', $url)) {
                    continue;
                }
                
                // Skip WordPress system URLs
                if (preg_match('/(wp-admin|wp-content|wp-includes|wp-json|wp-login\.php|wp-ajax\.php)/i', $url)) {
                    continue;
                }
                
                // Skip language switcher links (check full tag from matches[0])
                $full_tag = $matches[0][$key];
                if (preg_match('/(hreflang|lang)\s*=\s*[\'"][^\'"]*[\'"]/i', $full_tag)) {
                    continue;
                }
                
                // Add to the collection of URLs to be processed
                $urls[] = [
                    'original' => $url,
                    'full_tag' => $full_tag
                ];
            }
        }
        
        return $urls;
    }

    private function get_body_html_from_buffer($buffer) {
        if (preg_match('/<body.*?>(.*)<\/body>/is', $buffer, $matches)) {
            return $matches[1];
        }
        return $buffer;
    }

    private function insert_body_html_into_buffer($buffer, $body_html) {
        return preg_replace('/<body.*?>(.*)<\/body>/is', $body_html, $buffer);
    }

    /**
     * Normalize a URL to process all URLs the same way
     * 
     * @param string $url The URL to normalize
     * @return string The normalized URL
     */
    private function normalize_url($url) {
        $home_url = home_url();
        $site_url = site_url();
        
        // Parse the URL
        $parsed = parse_url($url);
        $path = $parsed['path'] ?? '';
        
        // Handle relative URLs first
        if (strpos($path, 'http') !== 0) {
            if (strpos($path, '/') === 0) {
                // Strip the leading slash as url_to_postid expects a relative path
                $path = ltrim($path, '/');
            }
            return $path;
        }
        
        // For absolute URLs, we need to make them relative to the home URL
        // Try home_url first
        if (strpos($path, $home_url) === 0) {
            $path = substr($path, strlen($home_url));
        } 
        // Then try site_url
        else if (strpos($path, $site_url) === 0) {
            $path = substr($path, strlen($site_url));
        }
        
        // Strip any remaining leading slashes
        $path = ltrim($path, '/');
        
        return $path;
    }

    /**
     * Try to determine if URL points to a term archive
     * 
     * @param string $url The URL to check
     * @return array|false Term data or false if not a term URL
     */
    private function get_term_from_url($url) {
        $parsed_url = parse_url($url);
        $path = isset($parsed_url['path']) ? $parsed_url['path'] : '';
        
        // Skip home page
        if (empty($path) || $path === '/') {
            return false;
        }
        
        // Get all public taxonomies
        $taxonomies = get_taxonomies(['public' => true], 'objects');
        
        foreach ($taxonomies as $taxonomy) {
            // Skip if taxonomy is not translated
            if (!pll_is_translated_taxonomy($taxonomy->name)) {
                continue;
            }
            
            $tax_base = $taxonomy->rewrite['slug'] ?? $taxonomy->name;
            
            // Check if URL matches a taxonomy pattern
            if (preg_match("#^/?{$tax_base}/([^/]+)/?$#", $path, $matches)) {
                $term_slug = $matches[1];
                $term_id = $this->get_term_without_language_filter($term_slug, $taxonomy->name);
                if ($term_id) {
                    return [
                        'term_id' => $term_id,
                        'taxonomy' => $taxonomy->name,
                    ];
                }
            }
        }
        return false;
    }

    
    /**
     * Get term ID by slug and taxonomy without the filtering of the current language
     *
     * @param string $slug     The term slug
     * @param string $taxonomy The taxonomy name
     * @return int|null        Term ID if found, null otherwise
     */
    public function get_term_without_language_filter($slug, $taxonomy) {
        global $wpdb;
        $sql = "SELECT {$wpdb->terms}.term_id FROM {$wpdb->terms} 
                JOIN {$wpdb->term_taxonomy} ON {$wpdb->terms}.term_id = {$wpdb->term_taxonomy}.term_id
                WHERE {$wpdb->terms}.slug = %s AND {$wpdb->term_taxonomy}.taxonomy = %s";
        $term_id = $wpdb->get_var($wpdb->prepare($sql, $slug, $taxonomy));
        return $term_id;
    }

    /**
     * Get post ID from URL, supporting custom post types
     * 
     * @param string $url The normalized URL
     * @return int|false Post ID if found, false otherwise
     */
    private function get_post_id_from_url($url) {
        // First try standard url_to_postid
        $post_id = url_to_postid($url);
        if ($post_id) {
            return $post_id;
        }

        // If that fails, try to find it manually
        global $wpdb;

        // Remove trailing slashes and .html if present
        $url = rtrim($url, '/');
        $url = preg_replace('/\.html$/', '', $url);

        // Split URL into parts
        $parts = explode('/', $url);
        $slug = end($parts);

        // Get all public post types
        $post_types = get_post_types(['public' => true]);
        $post_types_str = "'" . implode("','", array_map('esc_sql', $post_types)) . "'";

        // Try to find post by slug
        $query = $wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} 
            WHERE post_name = %s 
            AND post_type IN ({$post_types_str})
            AND post_status = 'publish'
            LIMIT 1",
            $slug
        );

        $result = $wpdb->get_var($query);

        return $result ? (int)$result : false;
    }

    /**
     * Filter metadata to replace URLs in string values
     * 
     * @param mixed $value The value to filter
     * @param int $object_id Object ID
     * @param string $meta_key Meta key
     * @param string $meta_type Type of metadata (post, term, user, comment)
     * @return mixed Filtered value
     */
    public function filter_metadata($value, $object_id, $meta_key, $meta_type) {
        // Skip if value is not set yet or if it's not a string/array
        if (null === $value || (!is_string($value) && !is_array($value))) {
            return $value;
        }

        // Handle arrays recursively
        if (is_array($value)) {
            return array_map(function($item) {
                if (is_string($item)) {
                    return $this->replace_urls($item);
                }
                return $item;
            }, $value);
        }

        // Handle strings
        if (is_string($value)) {
            return $this->replace_urls($value);
        }

        return $value;
    }

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
}

//Internal_Link_Replacer::get_instance();
